// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Logging/LogMacros.h"

#define CPS_VERSION 1

DECLARE_LOG_CATEGORY_EXTERN(LogCPSControlMessenger, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogCPSControlCommunication, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogCPSDiscoveryMessenger, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogCPSDiscoveryCommunication, Log, All);
DECLARE_LOG_CATEGORY_EXTERN(LogCPSExportHandler, Log, All);