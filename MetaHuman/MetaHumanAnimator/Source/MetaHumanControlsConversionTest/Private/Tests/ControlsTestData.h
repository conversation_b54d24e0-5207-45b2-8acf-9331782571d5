// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/UnrealString.h"

#if WITH_DEV_AUTOMATION_TESTS

namespace SolveControlsTestData
{
	const TMap<FString, float> InputSolveControls = {
		{"CTRL_L_brow_down.ty", 0.196608633},
		{"CTRL_R_brow_down.ty", 0.173116893},
		{"CTRL_L_brow_lateral.ty", 0.300757229},
		{"CTRL_R_brow_lateral.ty", 0.305577993},
		{"CTRL_L_brow_raiseIn.ty", 0.432218164},
		{"CTRL_R_brow_raiseIn.ty", 0.525324643},
		{"CTRL_L_brow_raiseOut.ty", 0.0792668462},
		{"CTRL_R_brow_raiseOut.ty", 0.312558264},
		{"CTRL_L_eye_blink.ty", 0.0349351168},
		{"CTRL_R_eye_blink.ty", 0.0373136997},
		{"CTRL_L_eye_squintInner.ty", 0.0114430785},
		{"CTRL_R_eye_squintInner.ty", 0.0211309791},
		{"CTRL_L_eye_cheekRaise.ty", -0.000972867012},
		{"CTRL_R_eye_cheekRaise.ty", 0.0168012381},
		{"CTRL_L_eye.ty", -0.0155423880},
		{"CTRL_R_eye.ty", -0.0155484676},
		{"CTRL_L_nose.ty", 5.78165054e-05},
		{"CTRL_R_nose.ty", -0.0483409762},
		{"CTRL_L_nose.tx", 0.105465174},
		{"CTRL_R_nose.tx", 0.101637244},
		{"CTRL_L_nose_nasolabialDeepen.ty", 0.00245118141},
		{"CTRL_R_nose_nasolabialDeepen.ty", -0.00367075205},
		{"CTRL_C_mouth.tx", 0.0331877470},
		{"CTRL_L_mouth_upperLipRaise.ty", 0.0575857460},
		{"CTRL_R_mouth_upperLipRaise.ty", 0.000115066767},
		{"CTRL_L_mouth_lowerLipDepress.ty", 0.00680816174},
		{"CTRL_R_mouth_lowerLipDepress.ty", -0.00431782007},
		{"CTRL_L_mouth_cornerPull.ty", 0.0917850435},
		{"CTRL_R_mouth_cornerPull.ty", 0.0310698748},
		{"CTRL_L_mouth_stretch.ty", 0.0117343664},
		{"CTRL_R_mouth_stretch.ty", 0.0187535286},
		{"CTRL_L_mouth_dimple.ty", 0.0212658942},
		{"CTRL_R_mouth_dimple.ty", 0.0631878376},
		{"CTRL_R_mouth_cornerDepress.ty", 0.123498976},
		{"CTRL_L_mouth_cornerDepress.ty", 0.145800620},
		{"CTRL_L_mouth_purseU.ty", 0.0270517170},
		{"CTRL_R_mouth_purseU.ty", 0.00591263175},
		{"CTRL_L_mouth_purseD.ty", 0.0550532341},
		{"CTRL_R_mouth_purseD.ty", 0.0844295919},
		{"CTRL_L_mouth_towardsU.ty", 0.0458836555},
		{"CTRL_R_mouth_towardsU.ty", 0.00826126337},
		{"CTRL_L_mouth_towardsD.ty", 0.00849410892},
		{"CTRL_R_mouth_towardsD.ty", 0.0167388618},
		{"CTRL_L_mouth_funnelU.ty", 0.0153377950},
		{"CTRL_R_mouth_funnelU.ty", -0.00374817848},
		{"CTRL_L_mouth_funnelD.ty", 0.00663277507},
		{"CTRL_R_mouth_funnelD.ty", 0.0122615695},
		{"CTRL_L_mouth_lipsTogetherU.ty", 0.0104329288},
		{"CTRL_R_mouth_lipsTogetherU.ty", 0.00727510452},
		{"CTRL_L_mouth_lipsTogetherD.ty", -0.00294858217},
		{"CTRL_R_mouth_lipsTogetherD.ty", -0.00170391798},
		{"CTRL_L_mouth_lipBiteU.ty", 0.0128311217},
		{"CTRL_R_mouth_lipBiteU.ty", 0.104377300},
		{"CTRL_L_mouth_lipBiteD.ty", 0.0161211789},
		{"CTRL_R_mouth_lipBiteD.ty", 0.0325355828},
		{"CTRL_L_mouth_sharpCornerPull.ty", 1.83284283e-05},
		{"CTRL_R_mouth_sharpCornerPull.ty", 0.00433504581},
		{"CTRL_L_mouth_pushPullU.ty", -0.108609557},
		{"CTRL_R_mouth_pushPullU.ty", -0.202033162},
		{"CTRL_L_mouth_pushPullD.ty", -0.188379407},
		{"CTRL_R_mouth_pushPullD.ty", -0.115025699},
		{"CTRL_L_mouth_cornerSharpnessU.ty", -0.0301781297},
		{"CTRL_R_mouth_cornerSharpnessU.ty", -0.0210517645},
		{"CTRL_L_mouth_cornerSharpnessD.ty", -0.00681740046},
		{"CTRL_R_mouth_cornerSharpnessD.ty", 0.0749087334},
		{"CTRL_L_mouth_lipsRollU.ty", -0.00350254774},
		{"CTRL_R_mouth_lipsRollU.ty", 0.0254632235},
		{"CTRL_L_mouth_lipsRollD.ty", -0.0142377615},
		{"CTRL_R_mouth_lipsRollD.ty", -0.0609505773},
		{"CTRL_C_jaw.ty", 0.0826545358},
		{"CTRL_C_jaw.tx", -0.0583701134},
		{"CTRL_C_jaw_fwdBack.ty", -0.00437319279},
		{"CTRL_L_jaw_ChinRaiseD.ty", 0.104629844},
		{"CTRL_R_jaw_ChinRaiseD.ty", 0.142034024},
		{"CTRL_C_tongue_move.ty", -0.257301092},
		{"CTRL_C_tongue_move.tx", 0.0177811533},
		{"CTRL_C_tongue_inOut.ty", -0.000454217196},
		{"CTRL_C_tongue_tipMove.ty", 0.185356975},
		{"CTRL_C_tongue_tipMove.tx", 0.00921810232},
		{"CTRL_C_tongue_wideNarrow.ty", 0.0628439933},
		{"CTRL_C_tongue_press.ty", 0.213894382},
		{"CTRL_C_tongue_roll.ty", 0.0180214047},
		{"CTRL_C_tongue_thickThin.ty", 0.500553429}
	};

	const TMap<FString, float> ExpectedRigControls = {
		{ "CTRL_expressions_browDownL", 0.196609 },
		{ "CTRL_expressions_browDownR", 0.173117 },
		{ "CTRL_expressions_browLateralL", 0.300757 },
		{ "CTRL_expressions_browLateralR", 0.305578 },
		{ "CTRL_expressions_browRaiseInL", 0.432218 },
		{ "CTRL_expressions_browRaiseInR", 0.525325 },
		{ "CTRL_expressions_browRaiseOuterL", 0.0792668 },
		{ "CTRL_expressions_browRaiseOuterR", 0.312558 },
		{ "CTRL_expressions_earUpL", 0 },
		{ "CTRL_expressions_earUpR", 0 },
		{ "CTRL_expressions_eyeBlinkL", 0.0349351 },
		{ "CTRL_expressions_eyeBlinkR", 0.0373137 },
		{ "CTRL_expressions_eyeCheekRaiseL", 0 },
		{ "CTRL_expressions_eyeCheekRaiseR", 0.0168012 },
		{ "CTRL_expressions_eyeFaceScrunchL", 0 },
		{ "CTRL_expressions_eyeFaceScrunchR", 0 },
		{ "CTRL_expressions_eyeLidPressL", 0 },
		{ "CTRL_expressions_eyeLidPressR", 0 },
		{ "CTRL_expressions_eyeLookDownL", 0.0155424 },
		{ "CTRL_expressions_eyeLookDownR", 0.0155485 },
		{ "CTRL_expressions_eyeLookLeftL", 0 },
		{ "CTRL_expressions_eyeLookLeftR", 0 },
		{ "CTRL_expressions_eyeLookRightL", 0 },
		{ "CTRL_expressions_eyeLookRightR", 0 },
		{ "CTRL_expressions_eyeLookUpL", 0 },
		{ "CTRL_expressions_eyeLookUpR", 0 },
		{ "CTRL_expressions_eyeLowerLidDownL", 0 },
		{ "CTRL_expressions_eyeLowerLidDownR", 0 },
		{ "CTRL_expressions_eyeLowerLidUpL", 0 },
		{ "CTRL_expressions_eyeLowerLidUpR", 0 },
		{ "CTRL_expressions_eyeParallelLookDirection", 0 },
		{ "CTRL_expressions_eyePupilNarrowL", 0 },
		{ "CTRL_expressions_eyePupilNarrowR", 0 },
		{ "CTRL_expressions_eyePupilWideL", 0 },
		{ "CTRL_expressions_eyePupilWideR", 0 },
		{ "CTRL_expressions_eyeRelaxL", 0 },
		{ "CTRL_expressions_eyeRelaxR", 0 },
		{ "CTRL_expressions_eyeSquintInnerL", 0.0114431 },
		{ "CTRL_expressions_eyeSquintInnerR", 0.021131 },
		{ "CTRL_expressions_eyeUpperLidUpL", 0 },
		{ "CTRL_expressions_eyeUpperLidUpR", 0 },
		{ "CTRL_expressions_eyeWidenL", 0 },
		{ "CTRL_expressions_eyeWidenR", 0 },
		{ "CTRL_expressions_eyelashesDownINL", 0 },
		{ "CTRL_expressions_eyelashesDownINR", 0 },
		{ "CTRL_expressions_eyelashesDownOUTL", 0 },
		{ "CTRL_expressions_eyelashesDownOUTR", 0 },
		{ "CTRL_expressions_eyelashesUpINL", 0 },
		{ "CTRL_expressions_eyelashesUpINR", 0 },
		{ "CTRL_expressions_eyelashesUpOUTL", 0 },
		{ "CTRL_expressions_eyelashesUpOUTR", 0 },
		{ "CTRL_expressions_jawBack", 0 },
		{ "CTRL_expressions_jawChinCompressL", 0 },
		{ "CTRL_expressions_jawChinCompressR", 0 },
		{ "CTRL_expressions_jawChinRaiseDL", 0.10463 },
		{ "CTRL_expressions_jawChinRaiseDR", 0.142034 },
		{ "CTRL_expressions_jawChinRaiseUL", 0 },
		{ "CTRL_expressions_jawChinRaiseUR", 0 },
		{ "CTRL_expressions_jawClenchL", 0 },
		{ "CTRL_expressions_jawClenchR", 0 },
		{ "CTRL_expressions_jawFwd", 0.00437319 },
		{ "CTRL_expressions_jawLeft", 0.0583701 },
		{ "CTRL_expressions_jawOpen", 0.0826545 },
		{ "CTRL_expressions_jawOpenExtreme", 0 },
		{ "CTRL_expressions_jawRight", 0 },
		{ "CTRL_expressions_mouthCheekBlowL", 0 },
		{ "CTRL_expressions_mouthCheekBlowR", 0 },
		{ "CTRL_expressions_mouthCheekSuckL", 0 },
		{ "CTRL_expressions_mouthCheekSuckR", 0 },
		{ "CTRL_expressions_mouthCornerDepressL", 0.145801 },
		{ "CTRL_expressions_mouthCornerDepressR", 0.123499 },
		{ "CTRL_expressions_mouthCornerDownL", 0 },
		{ "CTRL_expressions_mouthCornerDownR", 0 },
		{ "CTRL_expressions_mouthCornerNarrowL", 0 },
		{ "CTRL_expressions_mouthCornerNarrowR", 0 },
		{ "CTRL_expressions_mouthCornerPullL", 0.091785 },
		{ "CTRL_expressions_mouthCornerPullR", 0.0310699 },
		{ "CTRL_expressions_mouthCornerRounderDL", 0.0068174 },
		{ "CTRL_expressions_mouthCornerRounderDR", 0 },
		{ "CTRL_expressions_mouthCornerRounderUL", 0.0301781 },
		{ "CTRL_expressions_mouthCornerRounderUR", 0.0210518 },
		{ "CTRL_expressions_mouthCornerSharpenDL", 0 },
		{ "CTRL_expressions_mouthCornerSharpenDR", 0.0749087 },
		{ "CTRL_expressions_mouthCornerSharpenUL", 0 },
		{ "CTRL_expressions_mouthCornerSharpenUR", 0 },
		{ "CTRL_expressions_mouthCornerUpL", 0 },
		{ "CTRL_expressions_mouthCornerUpR", 0 },
		{ "CTRL_expressions_mouthCornerWideL", 0 },
		{ "CTRL_expressions_mouthCornerWideR", 0 },
		{ "CTRL_expressions_mouthDimpleL", 0.0212659 },
		{ "CTRL_expressions_mouthDimpleR", 0.0631878 },
		{ "CTRL_expressions_mouthDown", 0 },
		{ "CTRL_expressions_mouthFunnelDL", 0.00663278 },
		{ "CTRL_expressions_mouthFunnelDR", 0.0122616 },
		{ "CTRL_expressions_mouthFunnelUL", 0.0153378 },
		{ "CTRL_expressions_mouthFunnelUR", 0 },
		{ "CTRL_expressions_mouthLeft", 0.0331877 },
		{ "CTRL_expressions_mouthLipsBlowL", 0 },
		{ "CTRL_expressions_mouthLipsBlowR", 0 },
		{ "CTRL_expressions_mouthLipsPressL", 0 },
		{ "CTRL_expressions_mouthLipsPressR", 0 },
		{ "CTRL_expressions_mouthLipsPullDL", 0.188379 },
		{ "CTRL_expressions_mouthLipsPullDR", 0.115026 },
		{ "CTRL_expressions_mouthLipsPullUL", 0.10861 },
		{ "CTRL_expressions_mouthLipsPullUR", 0.202033 },
		{ "CTRL_expressions_mouthLipsPurseDL", 0.0550532 },
		{ "CTRL_expressions_mouthLipsPurseDR", 0.0844296 },
		{ "CTRL_expressions_mouthLipsPurseUL", 0.0270517 },
		{ "CTRL_expressions_mouthLipsPurseUR", 0.00591263 },
		{ "CTRL_expressions_mouthLipsPushDL", 0 },
		{ "CTRL_expressions_mouthLipsPushDR", 0 },
		{ "CTRL_expressions_mouthLipsPushUL", 0 },
		{ "CTRL_expressions_mouthLipsPushUR", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh3", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh3", 0 },
		{ "CTRL_expressions_mouthLipsThickDL", 0 },
		{ "CTRL_expressions_mouthLipsThickDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUR", 0 },
		{ "CTRL_expressions_mouthLipsThickUL", 0 },
		{ "CTRL_expressions_mouthLipsThickUR", 0 },
		{ "CTRL_expressions_mouthLipsThinDL", 0 },
		{ "CTRL_expressions_mouthLipsThinDR", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardDL", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardDR", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardUL", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardUR", 0 },
		{ "CTRL_expressions_mouthLipsThinUL", 0 },
		{ "CTRL_expressions_mouthLipsThinUR", 0 },
		{ "CTRL_expressions_mouthLipsTightenDL", 0 },
		{ "CTRL_expressions_mouthLipsTightenDR", 0 },
		{ "CTRL_expressions_mouthLipsTightenUL", 0 },
		{ "CTRL_expressions_mouthLipsTightenUR", 0 },
		{ "CTRL_expressions_mouthLipsTogetherDL", 0 },
		{ "CTRL_expressions_mouthLipsTogetherDR", 0 },
		{ "CTRL_expressions_mouthLipsTogetherUL", 0.0104329 },
		{ "CTRL_expressions_mouthLipsTogetherUR", 0.0072751 },
		{ "CTRL_expressions_mouthLipsTowardsDL", 0.00849411 },
		{ "CTRL_expressions_mouthLipsTowardsDR", 0.0167389 },
		{ "CTRL_expressions_mouthLipsTowardsUL", 0.0458837 },
		{ "CTRL_expressions_mouthLipsTowardsUR", 0.00826126 },
		{ "CTRL_expressions_mouthLowerLipBiteL", 0.0161212 },
		{ "CTRL_expressions_mouthLowerLipBiteR", 0.0325356 },
		{ "CTRL_expressions_mouthLowerLipDepressL", 0.00680816 },
		{ "CTRL_expressions_mouthLowerLipDepressR", 0 },
		{ "CTRL_expressions_mouthLowerLipRollInL", 0 },
		{ "CTRL_expressions_mouthLowerLipRollInR", 0 },
		{ "CTRL_expressions_mouthLowerLipRollOutL", 0.0142378 },
		{ "CTRL_expressions_mouthLowerLipRollOutR", 0.0609506 },
		{ "CTRL_expressions_mouthLowerLipShiftLeft", 0 },
		{ "CTRL_expressions_mouthLowerLipShiftRight", 0 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethL", 0 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethR", 0 },
		{ "CTRL_expressions_mouthPressDL", 0 },
		{ "CTRL_expressions_mouthPressDR", 0 },
		{ "CTRL_expressions_mouthPressUL", 0 },
		{ "CTRL_expressions_mouthPressUR", 0 },
		{ "CTRL_expressions_mouthRight", 0 },
		{ "CTRL_expressions_mouthSharpCornerPullL", 1.83284e-05 },
		{ "CTRL_expressions_mouthSharpCornerPullR", 0.00433505 },
		{ "CTRL_expressions_mouthStickyDC", 0 },
		{ "CTRL_expressions_mouthStickyDINL", 0 },
		{ "CTRL_expressions_mouthStickyDINR", 0 },
		{ "CTRL_expressions_mouthStickyDOUTL", 0 },
		{ "CTRL_expressions_mouthStickyDOUTR", 0 },
		{ "CTRL_expressions_mouthStickyUC", 0 },
		{ "CTRL_expressions_mouthStickyUINL", 0 },
		{ "CTRL_expressions_mouthStickyUINR", 0 },
		{ "CTRL_expressions_mouthStickyUOUTL", 0 },
		{ "CTRL_expressions_mouthStickyUOUTR", 0 },
		{ "CTRL_expressions_mouthStretchL", 0.0117344 },
		{ "CTRL_expressions_mouthStretchLipsCloseL", 0 },
		{ "CTRL_expressions_mouthStretchLipsCloseR", 0 },
		{ "CTRL_expressions_mouthStretchR", 0.0187535 },
		{ "CTRL_expressions_mouthUp", 0 },
		{ "CTRL_expressions_mouthUpperLipBiteL", 0.0128311 },
		{ "CTRL_expressions_mouthUpperLipBiteR", 0.104377 },
		{ "CTRL_expressions_mouthUpperLipRaiseL", 0.0575857 },
		{ "CTRL_expressions_mouthUpperLipRaiseR", 0.000115067 },
		{ "CTRL_expressions_mouthUpperLipRollInL", 0 },
		{ "CTRL_expressions_mouthUpperLipRollInR", 0.0254632 },
		{ "CTRL_expressions_mouthUpperLipRollOutL", 0.00350255 },
		{ "CTRL_expressions_mouthUpperLipRollOutR", 0 },
		{ "CTRL_expressions_mouthUpperLipShiftLeft", 0 },
		{ "CTRL_expressions_mouthUpperLipShiftRight", 0 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethL", 0 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethR", 0 },
		{ "CTRL_expressions_neckDigastricDown", 0 },
		{ "CTRL_expressions_neckDigastricUp", 0 },
		{ "CTRL_expressions_neckMastoidContractL", 0 },
		{ "CTRL_expressions_neckMastoidContractR", 0 },
		{ "CTRL_expressions_neckStretchL", 0 },
		{ "CTRL_expressions_neckStretchR", 0 },
		{ "CTRL_expressions_neckSwallowPh1", 0 },
		{ "CTRL_expressions_neckSwallowPh2", 0 },
		{ "CTRL_expressions_neckSwallowPh3", 0 },
		{ "CTRL_expressions_neckSwallowPh4", 0 },
		{ "CTRL_expressions_neckThroatDown", 0 },
		{ "CTRL_expressions_neckThroatExhale", 0 },
		{ "CTRL_expressions_neckThroatInhale", 0 },
		{ "CTRL_expressions_neckThroatUp", 0 },
		{ "CTRL_expressions_noseNasolabialDeepenL", 0.00245118 },
		{ "CTRL_expressions_noseNasolabialDeepenR", 0 },
		{ "CTRL_expressions_noseNostrilCompressL", 0 },
		{ "CTRL_expressions_noseNostrilCompressR", 0 },
		{ "CTRL_expressions_noseNostrilDepressL", 0 },
		{ "CTRL_expressions_noseNostrilDepressR", 0.048341 },
		{ "CTRL_expressions_noseNostrilDilateL", 0.105465 },
		{ "CTRL_expressions_noseNostrilDilateR", 0.101637 },
		{ "CTRL_expressions_noseWrinkleL", 5.78165e-05 },
		{ "CTRL_expressions_noseWrinkleR", 0 },
		{ "CTRL_expressions_noseWrinkleUpperL", 1 },
		{ "CTRL_expressions_noseWrinkleUpperR", 1 },
		{ "CTRL_expressions_teethBackD", 0 },
		{ "CTRL_expressions_teethBackU", 0 },
		{ "CTRL_expressions_teethDownD", 0 },
		{ "CTRL_expressions_teethDownU", 0 },
		{ "CTRL_expressions_teethFwdD", 0 },
		{ "CTRL_expressions_teethFwdU", 0 },
		{ "CTRL_expressions_teethLeftD", 0 },
		{ "CTRL_expressions_teethLeftU", 0 },
		{ "CTRL_expressions_teethRightD", 0 },
		{ "CTRL_expressions_teethRightU", 0 },
		{ "CTRL_expressions_teethUpD", 0 },
		{ "CTRL_expressions_teethUpU", 0 },
		{ "CTRL_expressions_tongueBendDown", 0 },
		{ "CTRL_expressions_tongueBendUp", 0 },
		{ "CTRL_expressions_tongueDown", 0.257301 },
		{ "CTRL_expressions_tongueIn", 0 },
		{ "CTRL_expressions_tongueLeft", 0.0177812 },
		{ "CTRL_expressions_tongueNarrow", 0 },
		{ "CTRL_expressions_tongueOut", 0.000454217 },
		{ "CTRL_expressions_tonguePress", 0.213894 },
		{ "CTRL_expressions_tongueRight", 0 },
		{ "CTRL_expressions_tongueRoll", 0.0180214 },
		{ "CTRL_expressions_tongueThick", 0.500553 },
		{ "CTRL_expressions_tongueThin", 0 },
		{ "CTRL_expressions_tongueTipDown", 0 },
		{ "CTRL_expressions_tongueTipLeft", 0.0092181 },
		{ "CTRL_expressions_tongueTipRight", 0 },
		{ "CTRL_expressions_tongueTipUp", 0.185357 },
		{ "CTRL_expressions_tongueTwistLeft", 0 },
		{ "CTRL_expressions_tongueTwistRight", 0 },
		{ "CTRL_expressions_tongueUp", 0 },
		{ "CTRL_expressions_tongueWide", 0.062844 }
	};
}

namespace MinControlsTestData
{
	const TMap<FString, float> InputSolveControls = {
		{ "CTRL_L_brow_down.ty", -1.0 },
		{ "CTRL_R_brow_down.ty", -1.0 },
		{ "CTRL_L_brow_lateral.ty", -1.0 },
		{ "CTRL_R_brow_lateral.ty", -1.0 },
		{ "CTRL_L_brow_raiseIn.ty", -1.0 },
		{ "CTRL_R_brow_raiseIn.ty", -1.0 },
		{ "CTRL_L_brow_raiseOut.ty", -1.0 },
		{ "CTRL_R_brow_raiseOut.ty", -1.0 },
		{ "CTRL_L_ear_up.ty", -1.0 },
		{ "CTRL_R_ear_up.ty", -1.0 },
		{ "CTRL_L_eye_blink.ty", -1.0 },
		{ "CTRL_R_eye_blink.ty", -1.0 },
		{ "CTRL_L_eye_lidPress.ty", -1.0 },
		{ "CTRL_R_eye_lidPress.ty", -1.0 },
		{ "CTRL_L_eye_squintInner.ty", -1.0 },
		{ "CTRL_R_eye_squintInner.ty", -1.0 },
		{ "CTRL_L_eye_cheekRaise.ty", -1.0 },
		{ "CTRL_R_eye_cheekRaise.ty", -1.0 },
		{ "CTRL_L_eye_faceScrunch.ty", -1.0 },
		{ "CTRL_R_eye_faceScrunch.ty", -1.0 },
		{ "CTRL_L_eye_eyelidU.ty", -1.0 },
		{ "CTRL_R_eye_eyelidU.ty", -1.0 },
		{ "CTRL_L_eye_eyelidD.ty", -1.0 },
		{ "CTRL_R_eye_eyelidD.ty", -1.0 },
		{ "CTRL_L_eye.ty", -1.0 },
		{ "CTRL_R_eye.ty", -1.0 },
		{ "CTRL_L_eye.tx", -1.0 },
		{ "CTRL_R_eye.tx", -1.0 },
		{ "CTRL_L_eye_pupil.ty", -1.0 },
		{ "CTRL_R_eye_pupil.ty", -1.0 },
		{ "CTRL_C_eye_parallelLook.ty", -1.0 },
		{ "CTRL_L_eyelashes_tweakerIn.ty", -1.0 },
		{ "CTRL_R_eyelashes_tweakerIn.ty", -1.0 },
		{ "CTRL_L_eyelashes_tweakerOut.ty", -1.0 },
		{ "CTRL_R_eyelashes_tweakerOut.ty", -1.0 },
		{ "CTRL_L_nose.ty", -1.0 },
		{ "CTRL_R_nose.ty", -1.0 },
		{ "CTRL_L_nose.tx", -1.0 },
		{ "CTRL_R_nose.tx", -1.0 },
		{ "CTRL_L_nose_wrinkleUpper.ty", -1.0 },
		{ "CTRL_R_nose_wrinkleUpper.ty", -1.0 },
		{ "CTRL_L_nose_nasolabialDeepen.ty", -1.0 },
		{ "CTRL_R_nose_nasolabialDeepen.ty", -1.0 },
		{ "CTRL_L_mouth_suckBlow.ty", -1.0 },
		{ "CTRL_R_mouth_suckBlow.ty", -1.0 },
		{ "CTRL_L_mouth_lipsBlow.ty", -1.0 },
		{ "CTRL_R_mouth_lipsBlow.ty", -1.0 },
		{ "CTRL_C_mouth.ty", -1.0 },
		{ "CTRL_C_mouth.tx", -1.0 },
		{ "CTRL_L_mouth_upperLipRaise.ty", -1.0 },
		{ "CTRL_R_mouth_upperLipRaise.ty", -1.0 },
		{ "CTRL_L_mouth_lowerLipDepress.ty", -1.0 },
		{ "CTRL_R_mouth_lowerLipDepress.ty", -1.0 },
		{ "CTRL_L_mouth_cornerPull.ty", -1.0 },
		{ "CTRL_R_mouth_cornerPull.ty", -1.0 },
		{ "CTRL_L_mouth_stretch.ty", -1.0 },
		{ "CTRL_R_mouth_stretch.ty", -1.0 },
		{ "CTRL_L_mouth_stretchLipsClose.ty", -1.0 },
		{ "CTRL_R_mouth_stretchLipsClose.ty", -1.0 },
		{ "CTRL_L_mouth_dimple.ty", -1.0 },
		{ "CTRL_R_mouth_dimple.ty", -1.0 },
		{ "CTRL_L_mouth_cornerDepress.ty", -1.0 },
		{ "CTRL_R_mouth_cornerDepress.ty", -1.0 },
		{ "CTRL_L_mouth_pressU.ty", -1.0 },
		{ "CTRL_R_mouth_pressU.ty", -1.0 },
		{ "CTRL_L_mouth_pressD.ty", -1.0 },
		{ "CTRL_R_mouth_pressD.ty", -1.0 },
		{ "CTRL_L_mouth_purseU.ty", -1.0 },
		{ "CTRL_R_mouth_purseU.ty", -1.0 },
		{ "CTRL_L_mouth_purseD.ty", -1.0 },
		{ "CTRL_R_mouth_purseD.ty", -1.0 },
		{ "CTRL_L_mouth_towardsU.ty", -1.0 },
		{ "CTRL_R_mouth_towardsU.ty", -1.0 },
		{ "CTRL_L_mouth_towardsD.ty", -1.0 },
		{ "CTRL_R_mouth_towardsD.ty", -1.0 },
		{ "CTRL_L_mouth_funnelU.ty", -1.0 },
		{ "CTRL_R_mouth_funnelU.ty", -1.0 },
		{ "CTRL_L_mouth_funnelD.ty", -1.0 },
		{ "CTRL_R_mouth_funnelD.ty", -1.0 },
		{ "CTRL_L_mouth_lipsTogetherU.ty", -1.0 },
		{ "CTRL_R_mouth_lipsTogetherU.ty", -1.0 },
		{ "CTRL_L_mouth_lipsTogetherD.ty", -1.0 },
		{ "CTRL_R_mouth_lipsTogetherD.ty", -1.0 },
		{ "CTRL_L_mouth_lipBiteU.ty", -1.0 },
		{ "CTRL_R_mouth_lipBiteU.ty", -1.0 },
		{ "CTRL_L_mouth_lipBiteD.ty", -1.0 },
		{ "CTRL_R_mouth_lipBiteD.ty", -1.0 },
		{ "CTRL_L_mouth_tightenU.ty", -1.0 },
		{ "CTRL_R_mouth_tightenU.ty", -1.0 },
		{ "CTRL_L_mouth_tightenD.ty", -1.0 },
		{ "CTRL_R_mouth_tightenD.ty", -1.0 },
		{ "CTRL_L_mouth_lipsPressU.ty", -1.0 },
		{ "CTRL_R_mouth_lipsPressU.ty", -1.0 },
		{ "CTRL_L_mouth_sharpCornerPull.ty", -1.0 },
		{ "CTRL_R_mouth_sharpCornerPull.ty", -1.0 },
		{ "CTRL_C_mouth_stickyU.ty", -1.0 },
		{ "CTRL_L_mouth_stickyInnerU.ty", -1.0 },
		{ "CTRL_R_mouth_stickyInnerU.ty", -1.0 },
		{ "CTRL_L_mouth_stickyOuterU.ty", -1.0 },
		{ "CTRL_R_mouth_stickyOuterU.ty", -1.0 },
		{ "CTRL_C_mouth_stickyD.ty", -1.0 },
		{ "CTRL_L_mouth_stickyInnerD.ty", -1.0 },
		{ "CTRL_R_mouth_stickyInnerD.ty", -1.0 },
		{ "CTRL_L_mouth_stickyOuterD.ty", -1.0 },
		{ "CTRL_R_mouth_stickyOuterD.ty", -1.0 },
		{ "CTRL_L_mouth_lipSticky.ty", -1.0 },
		{ "CTRL_R_mouth_lipSticky.ty", -1.0 },
		{ "CTRL_L_mouth_pushPullU.ty", -1.0 },
		{ "CTRL_R_mouth_pushPullU.ty", -1.0 },
		{ "CTRL_L_mouth_pushPullD.ty", -1.0 },
		{ "CTRL_R_mouth_pushPullD.ty", -1.0 },
		{ "CTRL_L_mouth_thicknessU.ty", -1.0 },
		{ "CTRL_R_mouth_thicknessU.ty", -1.0 },
		{ "CTRL_L_mouth_thicknessD.ty", -1.0 },
		{ "CTRL_R_mouth_thicknessD.ty", -1.0 },
		{ "CTRL_L_mouth_thicknessInwardU.ty", -1.0 },
		{ "CTRL_R_mouth_thicknessInwardU.ty", -1.0 },
		{ "CTRL_L_mouth_thicknessInwardD.ty", -1.0 },
		{ "CTRL_R_mouth_thicknessInwardD.ty", -1.0 },
		{ "CTRL_L_mouth_cornerSharpnessU.ty", -1.0 },
		{ "CTRL_R_mouth_cornerSharpnessU.ty", -1.0 },
		{ "CTRL_L_mouth_cornerSharpnessD.ty", -1.0 },
		{ "CTRL_R_mouth_cornerSharpnessD.ty", -1.0 },
		{ "CTRL_L_mouth_lipsTowardsTeethU.ty", -1.0 },
		{ "CTRL_R_mouth_lipsTowardsTeethU.ty", -1.0 },
		{ "CTRL_L_mouth_lipsTowardsTeethD.ty", -1.0 },
		{ "CTRL_R_mouth_lipsTowardsTeethD.ty", -1.0 },
		{ "CTRL_C_mouth_lipShiftU.ty", -1.0 },
		{ "CTRL_C_mouth_lipShiftD.ty", -1.0 },
		{ "CTRL_L_mouth_lipsRollU.ty", -1.0 },
		{ "CTRL_R_mouth_lipsRollU.ty", -1.0 },
		{ "CTRL_L_mouth_lipsRollD.ty", -1.0 },
		{ "CTRL_R_mouth_lipsRollD.ty", -1.0 },
		{ "CTRL_L_mouth_corner.ty", -1.0 },
		{ "CTRL_L_mouth_corner.tx", -1.0 },
		{ "CTRL_R_mouth_corner.ty", -1.0 },
		{ "CTRL_R_mouth_corner.tx", -1.0 },
		{ "CTRL_C_tongue_inOut.ty", -1.0 },
		{ "CTRL_C_tongue_move.ty", -1.0 },
		{ "CTRL_C_tongue_move.tx", -1.0 },
		{ "CTRL_C_tongue_press.ty", -1.0 },
		{ "CTRL_C_tongue_wideNarrow.ty", -1.0 },
		{ "CTRL_C_tongue_bendTwist.ty", -1.0 },
		{ "CTRL_C_tongue_bendTwist.tx", -1.0 },
		{ "CTRL_C_tongue_roll.ty", -1.0 },
		{ "CTRL_C_tongue_tipMove.ty", -1.0 },
		{ "CTRL_C_tongue_tipMove.tx", -1.0 },
		{ "CTRL_C_tongue_thickThin.ty", -1.0 },
		{ "CTRL_C_jaw.ty", -1.0 },
		{ "CTRL_C_jaw.tx", -1.0 },
		{ "CTRL_C_jaw_fwdBack.ty", -1.0 },
		{ "CTRL_L_jaw_clench.ty", -1.0 },
		{ "CTRL_R_jaw_clench.ty", -1.0 },
		{ "CTRL_L_jaw_ChinRaiseU.ty", -1.0 },
		{ "CTRL_R_jaw_ChinRaiseU.ty", -1.0 },
		{ "CTRL_L_jaw_ChinRaiseD.ty", -1.0 },
		{ "CTRL_R_jaw_ChinRaiseD.ty", -1.0 },
		{ "CTRL_L_jaw_chinCompress.ty", -1.0 },
		{ "CTRL_R_jaw_chinCompress.ty", -1.0 },
		{ "CTRL_C_jaw_openExtreme.ty", -1.0 },
		{ "CTRL_L_neck_stretch.ty", -1.0 },
		{ "CTRL_R_neck_stretch.ty", -1.0 },
		{ "CTRL_C_neck_swallow.ty", -1.0 },
		{ "CTRL_L_neck_mastoidContract.ty", -1.0 },
		{ "CTRL_R_neck_mastoidContract.ty", -1.0 },
		{ "CTRL_neck_throatUpDown.ty", -1.0 },
		{ "CTRL_neck_digastricUpDown.ty", -1.0 },
		{ "CTRL_neck_throatExhaleInhale.ty", -1.0 },
		{ "CTRL_C_teethU.ty", -1.0 },
		{ "CTRL_C_teethU.tx", -1.0 },
		{ "CTRL_C_teeth_fwdBackU.ty", -1.0 },
		{ "CTRL_C_teethD.ty", -1.0 },
		{ "CTRL_C_teethD.tx", -1.0 },
		{ "CTRL_C_teeth_fwdBackD.ty", -1.0 },
	};

	const TMap<FString, float> ExpectedRigControls = {
		{ "CTRL_expressions_browDownL", 0 },
		{ "CTRL_expressions_browDownR", 0 },
		{ "CTRL_expressions_browLateralL", 0 },
		{ "CTRL_expressions_browLateralR", 0 },
		{ "CTRL_expressions_browRaiseInL", 0 },
		{ "CTRL_expressions_browRaiseInR", 0 },
		{ "CTRL_expressions_browRaiseOuterL", 0 },
		{ "CTRL_expressions_browRaiseOuterR", 0 },
		{ "CTRL_expressions_earUpL", 0 },
		{ "CTRL_expressions_earUpR", 0 },
		{ "CTRL_expressions_eyeBlinkL", 0 },
		{ "CTRL_expressions_eyeBlinkR", 0 },
		{ "CTRL_expressions_eyeCheekRaiseL", 0 },
		{ "CTRL_expressions_eyeCheekRaiseR", 0 },
		{ "CTRL_expressions_eyeFaceScrunchL", 0 },
		{ "CTRL_expressions_eyeFaceScrunchR", 0 },
		{ "CTRL_expressions_eyeLidPressL", 0 },
		{ "CTRL_expressions_eyeLidPressR", 0 },
		{ "CTRL_expressions_eyeLookDownL", 1 },
		{ "CTRL_expressions_eyeLookDownR", 1 },
		{ "CTRL_expressions_eyeLookLeftL", 0 },
		{ "CTRL_expressions_eyeLookLeftR", 0 },
		{ "CTRL_expressions_eyeLookRightL", 1 },
		{ "CTRL_expressions_eyeLookRightR", 1 },
		{ "CTRL_expressions_eyeLookUpL", 0 },
		{ "CTRL_expressions_eyeLookUpR", 0 },
		{ "CTRL_expressions_eyeLowerLidDownL", 1 },
		{ "CTRL_expressions_eyeLowerLidDownR", 1 },
		{ "CTRL_expressions_eyeLowerLidUpL", 0 },
		{ "CTRL_expressions_eyeLowerLidUpR", 0 },
		{ "CTRL_expressions_eyeParallelLookDirection", 0 },
		{ "CTRL_expressions_eyePupilNarrowL", 1 },
		{ "CTRL_expressions_eyePupilNarrowR", 1 },
		{ "CTRL_expressions_eyePupilWideL", 0 },
		{ "CTRL_expressions_eyePupilWideR", 0 },
		{ "CTRL_expressions_eyeRelaxL", 0 },
		{ "CTRL_expressions_eyeRelaxR", 0 },
		{ "CTRL_expressions_eyeSquintInnerL", 0 },
		{ "CTRL_expressions_eyeSquintInnerR", 0 },
		{ "CTRL_expressions_eyeUpperLidUpL", 1 },
		{ "CTRL_expressions_eyeUpperLidUpR", 1 },
		{ "CTRL_expressions_eyeWidenL", 1 },
		{ "CTRL_expressions_eyeWidenR", 1 },
		{ "CTRL_expressions_eyelashesDownINL", 0 },
		{ "CTRL_expressions_eyelashesDownINR", 0 },
		{ "CTRL_expressions_eyelashesDownOUTL", 0 },
		{ "CTRL_expressions_eyelashesDownOUTR", 0 },
		{ "CTRL_expressions_eyelashesUpINL", 1 },
		{ "CTRL_expressions_eyelashesUpINR", 1 },
		{ "CTRL_expressions_eyelashesUpOUTL", 1 },
		{ "CTRL_expressions_eyelashesUpOUTR", 1 },
		{ "CTRL_expressions_jawBack", 0 },
		{ "CTRL_expressions_jawChinCompressL", 0 },
		{ "CTRL_expressions_jawChinCompressR", 0 },
		{ "CTRL_expressions_jawChinRaiseDL", 0 },
		{ "CTRL_expressions_jawChinRaiseDR", 0 },
		{ "CTRL_expressions_jawChinRaiseUL", 0 },
		{ "CTRL_expressions_jawChinRaiseUR", 0 },
		{ "CTRL_expressions_jawClenchL", 0 },
		{ "CTRL_expressions_jawClenchR", 0 },
		{ "CTRL_expressions_jawFwd", 1 },
		{ "CTRL_expressions_jawLeft", 1 },
		{ "CTRL_expressions_jawOpen", 0 },
		{ "CTRL_expressions_jawOpenExtreme", 0 },
		{ "CTRL_expressions_jawRight", 0 },
		{ "CTRL_expressions_mouthCheekBlowL", 0 },
		{ "CTRL_expressions_mouthCheekBlowR", 0 },
		{ "CTRL_expressions_mouthCheekSuckL", 1 },
		{ "CTRL_expressions_mouthCheekSuckR", 1 },
		{ "CTRL_expressions_mouthCornerDepressL", 0 },
		{ "CTRL_expressions_mouthCornerDepressR", 0 },
		{ "CTRL_expressions_mouthCornerDownL", 1 },
		{ "CTRL_expressions_mouthCornerDownR", 1 },
		{ "CTRL_expressions_mouthCornerNarrowL", 1 },
		{ "CTRL_expressions_mouthCornerNarrowR", 1 },
		{ "CTRL_expressions_mouthCornerPullL", 0 },
		{ "CTRL_expressions_mouthCornerPullR", 0 },
		{ "CTRL_expressions_mouthCornerRounderDL", 1 },
		{ "CTRL_expressions_mouthCornerRounderDR", 1 },
		{ "CTRL_expressions_mouthCornerRounderUL", 1 },
		{ "CTRL_expressions_mouthCornerRounderUR", 1 },
		{ "CTRL_expressions_mouthCornerSharpenDL", 0 },
		{ "CTRL_expressions_mouthCornerSharpenDR", 0 },
		{ "CTRL_expressions_mouthCornerSharpenUL", 0 },
		{ "CTRL_expressions_mouthCornerSharpenUR", 0 },
		{ "CTRL_expressions_mouthCornerUpL", 0 },
		{ "CTRL_expressions_mouthCornerUpR", 0 },
		{ "CTRL_expressions_mouthCornerWideL", 0 },
		{ "CTRL_expressions_mouthCornerWideR", 0 },
		{ "CTRL_expressions_mouthDimpleL", 0 },
		{ "CTRL_expressions_mouthDimpleR", 0 },
		{ "CTRL_expressions_mouthDown", 1 },
		{ "CTRL_expressions_mouthFunnelDL", 0 },
		{ "CTRL_expressions_mouthFunnelDR", 0 },
		{ "CTRL_expressions_mouthFunnelUL", 0 },
		{ "CTRL_expressions_mouthFunnelUR", 0 },
		{ "CTRL_expressions_mouthLeft", 0 },
		{ "CTRL_expressions_mouthLipsBlowL", 0 },
		{ "CTRL_expressions_mouthLipsBlowR", 0 },
		{ "CTRL_expressions_mouthLipsPressL", 0 },
		{ "CTRL_expressions_mouthLipsPressR", 0 },
		{ "CTRL_expressions_mouthLipsPullDL", 1 },
		{ "CTRL_expressions_mouthLipsPullDR", 1 },
		{ "CTRL_expressions_mouthLipsPullUL", 1 },
		{ "CTRL_expressions_mouthLipsPullUR", 1 },
		{ "CTRL_expressions_mouthLipsPurseDL", 0 },
		{ "CTRL_expressions_mouthLipsPurseDR", 0 },
		{ "CTRL_expressions_mouthLipsPurseUL", 0 },
		{ "CTRL_expressions_mouthLipsPurseUR", 0 },
		{ "CTRL_expressions_mouthLipsPushDL", 0 },
		{ "CTRL_expressions_mouthLipsPushDR", 0 },
		{ "CTRL_expressions_mouthLipsPushUL", 0 },
		{ "CTRL_expressions_mouthLipsPushUR", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh3", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh3", 0 },
		{ "CTRL_expressions_mouthLipsThickDL", 1 },
		{ "CTRL_expressions_mouthLipsThickDR", 1 },
		{ "CTRL_expressions_mouthLipsThickInwardDL", 1 },
		{ "CTRL_expressions_mouthLipsThickInwardDR", 1 },
		{ "CTRL_expressions_mouthLipsThickInwardUL", 1 },
		{ "CTRL_expressions_mouthLipsThickInwardUR", 1 },
		{ "CTRL_expressions_mouthLipsThickUL", 1 },
		{ "CTRL_expressions_mouthLipsThickUR", 1 },
		{ "CTRL_expressions_mouthLipsThinDL", 0 },
		{ "CTRL_expressions_mouthLipsThinDR", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardDL", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardDR", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardUL", 0 },
		{ "CTRL_expressions_mouthLipsThinInwardUR", 0 },
		{ "CTRL_expressions_mouthLipsThinUL", 0 },
		{ "CTRL_expressions_mouthLipsThinUR", 0 },
		{ "CTRL_expressions_mouthLipsTightenDL", 0 },
		{ "CTRL_expressions_mouthLipsTightenDR", 0 },
		{ "CTRL_expressions_mouthLipsTightenUL", 0 },
		{ "CTRL_expressions_mouthLipsTightenUR", 0 },
		{ "CTRL_expressions_mouthLipsTogetherDL", 0 },
		{ "CTRL_expressions_mouthLipsTogetherDR", 0 },
		{ "CTRL_expressions_mouthLipsTogetherUL", 0 },
		{ "CTRL_expressions_mouthLipsTogetherUR", 0 },
		{ "CTRL_expressions_mouthLipsTowardsDL", 0 },
		{ "CTRL_expressions_mouthLipsTowardsDR", 0 },
		{ "CTRL_expressions_mouthLipsTowardsUL", 0 },
		{ "CTRL_expressions_mouthLipsTowardsUR", 0 },
		{ "CTRL_expressions_mouthLowerLipBiteL", 0 },
		{ "CTRL_expressions_mouthLowerLipBiteR", 0 },
		{ "CTRL_expressions_mouthLowerLipDepressL", 0 },
		{ "CTRL_expressions_mouthLowerLipDepressR", 0 },
		{ "CTRL_expressions_mouthLowerLipRollInL", 0 },
		{ "CTRL_expressions_mouthLowerLipRollInR", 0 },
		{ "CTRL_expressions_mouthLowerLipRollOutL", 1 },
		{ "CTRL_expressions_mouthLowerLipRollOutR", 1 },
		{ "CTRL_expressions_mouthLowerLipShiftLeft", 0 },
		{ "CTRL_expressions_mouthLowerLipShiftRight", 1 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethL", 0 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethR", 0 },
		{ "CTRL_expressions_mouthPressDL", 0 },
		{ "CTRL_expressions_mouthPressDR", 0 },
		{ "CTRL_expressions_mouthPressUL", 0 },
		{ "CTRL_expressions_mouthPressUR", 0 },
		{ "CTRL_expressions_mouthRight", 1 },
		{ "CTRL_expressions_mouthSharpCornerPullL", 0 },
		{ "CTRL_expressions_mouthSharpCornerPullR", 0 },
		{ "CTRL_expressions_mouthStickyDC", 0 },
		{ "CTRL_expressions_mouthStickyDINL", 0 },
		{ "CTRL_expressions_mouthStickyDINR", 0 },
		{ "CTRL_expressions_mouthStickyDOUTL", 0 },
		{ "CTRL_expressions_mouthStickyDOUTR", 0 },
		{ "CTRL_expressions_mouthStickyUC", 0 },
		{ "CTRL_expressions_mouthStickyUINL", 0 },
		{ "CTRL_expressions_mouthStickyUINR", 0 },
		{ "CTRL_expressions_mouthStickyUOUTL", 0 },
		{ "CTRL_expressions_mouthStickyUOUTR", 0 },
		{ "CTRL_expressions_mouthStretchL", 0 },
		{ "CTRL_expressions_mouthStretchLipsCloseL", 0 },
		{ "CTRL_expressions_mouthStretchLipsCloseR", 0 },
		{ "CTRL_expressions_mouthStretchR", 0 },
		{ "CTRL_expressions_mouthUp", 0 },
		{ "CTRL_expressions_mouthUpperLipBiteL", 0 },
		{ "CTRL_expressions_mouthUpperLipBiteR", 0 },
		{ "CTRL_expressions_mouthUpperLipRaiseL", 0 },
		{ "CTRL_expressions_mouthUpperLipRaiseR", 0 },
		{ "CTRL_expressions_mouthUpperLipRollInL", 0 },
		{ "CTRL_expressions_mouthUpperLipRollInR", 0 },
		{ "CTRL_expressions_mouthUpperLipRollOutL", 1 },
		{ "CTRL_expressions_mouthUpperLipRollOutR", 1 },
		{ "CTRL_expressions_mouthUpperLipShiftLeft", 0 },
		{ "CTRL_expressions_mouthUpperLipShiftRight", 1 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethL", 0 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethR", 0 },
		{ "CTRL_expressions_neckDigastricDown", 1 },
		{ "CTRL_expressions_neckDigastricUp", 0 },
		{ "CTRL_expressions_neckMastoidContractL", 0 },
		{ "CTRL_expressions_neckMastoidContractR", 0 },
		{ "CTRL_expressions_neckStretchL", 0 },
		{ "CTRL_expressions_neckStretchR", 0 },
		{ "CTRL_expressions_neckSwallowPh1", 0 },
		{ "CTRL_expressions_neckSwallowPh2", 0 },
		{ "CTRL_expressions_neckSwallowPh3", 0 },
		{ "CTRL_expressions_neckSwallowPh4", 0 },
		{ "CTRL_expressions_neckThroatDown", 1 },
		{ "CTRL_expressions_neckThroatExhale", 1 },
		{ "CTRL_expressions_neckThroatInhale", 0 },
		{ "CTRL_expressions_neckThroatUp", 0 },
		{ "CTRL_expressions_noseNasolabialDeepenL", 0 },
		{ "CTRL_expressions_noseNasolabialDeepenR", 0 },
		{ "CTRL_expressions_noseNostrilCompressL", 1 },
		{ "CTRL_expressions_noseNostrilCompressR", 1 },
		{ "CTRL_expressions_noseNostrilDepressL", 1 },
		{ "CTRL_expressions_noseNostrilDepressR", 1 },
		{ "CTRL_expressions_noseNostrilDilateL", 0 },
		{ "CTRL_expressions_noseNostrilDilateR", 0 },
		{ "CTRL_expressions_noseWrinkleL", 0 },
		{ "CTRL_expressions_noseWrinkleR", 0 },
		{ "CTRL_expressions_noseWrinkleUpperL", 0 },
		{ "CTRL_expressions_noseWrinkleUpperR", 0 },
		{ "CTRL_expressions_teethBackD", 0 },
		{ "CTRL_expressions_teethBackU", 0 },
		{ "CTRL_expressions_teethDownD", 1 },
		{ "CTRL_expressions_teethDownU", 1 },
		{ "CTRL_expressions_teethFwdD", 1 },
		{ "CTRL_expressions_teethFwdU", 1 },
		{ "CTRL_expressions_teethLeftD", 0 },
		{ "CTRL_expressions_teethLeftU", 0 },
		{ "CTRL_expressions_teethRightD", 1 },
		{ "CTRL_expressions_teethRightU", 1 },
		{ "CTRL_expressions_teethUpD", 0 },
		{ "CTRL_expressions_teethUpU", 0 },
		{ "CTRL_expressions_tongueBendDown", 1 },
		{ "CTRL_expressions_tongueBendUp", 0 },
		{ "CTRL_expressions_tongueDown", 1 },
		{ "CTRL_expressions_tongueIn", 0 },
		{ "CTRL_expressions_tongueLeft", 0 },
		{ "CTRL_expressions_tongueNarrow", 1 },
		{ "CTRL_expressions_tongueOut", 1 },
		{ "CTRL_expressions_tonguePress", 0 },
		{ "CTRL_expressions_tongueRight", 1 },
		{ "CTRL_expressions_tongueRoll", 0 },
		{ "CTRL_expressions_tongueThick", 0 },
		{ "CTRL_expressions_tongueThin", 1 },
		{ "CTRL_expressions_tongueTipDown", 1 },
		{ "CTRL_expressions_tongueTipLeft", 0 },
		{ "CTRL_expressions_tongueTipRight", 1 },
		{ "CTRL_expressions_tongueTipUp", 0 },
		{ "CTRL_expressions_tongueTwistLeft", 0 },
		{ "CTRL_expressions_tongueTwistRight", 1 },
		{ "CTRL_expressions_tongueUp", 0 },
		{ "CTRL_expressions_tongueWide", 0 }
	};
}

namespace MaxControlsTestData
{
	const TMap<FString, float> InputSolveControls = {
		{ "CTRL_L_brow_down.ty", 1.0 },
		{ "CTRL_R_brow_down.ty", 1.0 },
		{ "CTRL_L_brow_lateral.ty", 1.0 },
		{ "CTRL_R_brow_lateral.ty", 1.0 },
		{ "CTRL_L_brow_raiseIn.ty", 1.0 },
		{ "CTRL_R_brow_raiseIn.ty", 1.0 },
		{ "CTRL_L_brow_raiseOut.ty", 1.0 },
		{ "CTRL_R_brow_raiseOut.ty", 1.0 },
		{ "CTRL_L_ear_up.ty", 1.0 },
		{ "CTRL_R_ear_up.ty", 1.0 },
		{ "CTRL_L_eye_blink.ty", 1.0 },
		{ "CTRL_R_eye_blink.ty", 1.0 },
		{ "CTRL_L_eye_lidPress.ty", 1.0 },
		{ "CTRL_R_eye_lidPress.ty", 1.0 },
		{ "CTRL_L_eye_squintInner.ty", 1.0 },
		{ "CTRL_R_eye_squintInner.ty", 1.0 },
		{ "CTRL_L_eye_cheekRaise.ty", 1.0 },
		{ "CTRL_R_eye_cheekRaise.ty", 1.0 },
		{ "CTRL_L_eye_faceScrunch.ty", 1.0 },
		{ "CTRL_R_eye_faceScrunch.ty", 1.0 },
		{ "CTRL_L_eye_eyelidU.ty", 1.0 },
		{ "CTRL_R_eye_eyelidU.ty", 1.0 },
		{ "CTRL_L_eye_eyelidD.ty", 1.0 },
		{ "CTRL_R_eye_eyelidD.ty", 1.0 },
		{ "CTRL_L_eye.ty", 1.0 },
		{ "CTRL_R_eye.ty", 1.0 },
		{ "CTRL_L_eye.tx", 1.0 },
		{ "CTRL_R_eye.tx", 1.0 },
		{ "CTRL_L_eye_pupil.ty", 1.0 },
		{ "CTRL_R_eye_pupil.ty", 1.0 },
		{ "CTRL_C_eye_parallelLook.ty", 1.0 },
		{ "CTRL_L_eyelashes_tweakerIn.ty", 1.0 },
		{ "CTRL_R_eyelashes_tweakerIn.ty", 1.0 },
		{ "CTRL_L_eyelashes_tweakerOut.ty", 1.0 },
		{ "CTRL_R_eyelashes_tweakerOut.ty", 1.0 },
		{ "CTRL_L_nose.ty", 1.0 },
		{ "CTRL_R_nose.ty", 1.0 },
		{ "CTRL_L_nose.tx", 1.0 },
		{ "CTRL_R_nose.tx", 1.0 },
		{ "CTRL_L_nose_wrinkleUpper.ty", 1.0 },
		{ "CTRL_R_nose_wrinkleUpper.ty", 1.0 },
		{ "CTRL_L_nose_nasolabialDeepen.ty", 1.0 },
		{ "CTRL_R_nose_nasolabialDeepen.ty", 1.0 },
		{ "CTRL_L_mouth_suckBlow.ty", 1.0 },
		{ "CTRL_R_mouth_suckBlow.ty", 1.0 },
		{ "CTRL_L_mouth_lipsBlow.ty", 1.0 },
		{ "CTRL_R_mouth_lipsBlow.ty", 1.0 },
		{ "CTRL_C_mouth.ty", 1.0 },
		{ "CTRL_C_mouth.tx", 1.0 },
		{ "CTRL_L_mouth_upperLipRaise.ty", 1.0 },
		{ "CTRL_R_mouth_upperLipRaise.ty", 1.0 },
		{ "CTRL_L_mouth_lowerLipDepress.ty", 1.0 },
		{ "CTRL_R_mouth_lowerLipDepress.ty", 1.0 },
		{ "CTRL_L_mouth_cornerPull.ty", 1.0 },
		{ "CTRL_R_mouth_cornerPull.ty", 1.0 },
		{ "CTRL_L_mouth_stretch.ty", 1.0 },
		{ "CTRL_R_mouth_stretch.ty", 1.0 },
		{ "CTRL_L_mouth_stretchLipsClose.ty", 1.0 },
		{ "CTRL_R_mouth_stretchLipsClose.ty", 1.0 },
		{ "CTRL_L_mouth_dimple.ty", 1.0 },
		{ "CTRL_R_mouth_dimple.ty", 1.0 },
		{ "CTRL_L_mouth_cornerDepress.ty", 1.0 },
		{ "CTRL_R_mouth_cornerDepress.ty", 1.0 },
		{ "CTRL_L_mouth_pressU.ty", 1.0 },
		{ "CTRL_R_mouth_pressU.ty", 1.0 },
		{ "CTRL_L_mouth_pressD.ty", 1.0 },
		{ "CTRL_R_mouth_pressD.ty", 1.0 },
		{ "CTRL_L_mouth_purseU.ty", 1.0 },
		{ "CTRL_R_mouth_purseU.ty", 1.0 },
		{ "CTRL_L_mouth_purseD.ty", 1.0 },
		{ "CTRL_R_mouth_purseD.ty", 1.0 },
		{ "CTRL_L_mouth_towardsU.ty", 1.0 },
		{ "CTRL_R_mouth_towardsU.ty", 1.0 },
		{ "CTRL_L_mouth_towardsD.ty", 1.0 },
		{ "CTRL_R_mouth_towardsD.ty", 1.0 },
		{ "CTRL_L_mouth_funnelU.ty", 1.0 },
		{ "CTRL_R_mouth_funnelU.ty", 1.0 },
		{ "CTRL_L_mouth_funnelD.ty", 1.0 },
		{ "CTRL_R_mouth_funnelD.ty", 1.0 },
		{ "CTRL_L_mouth_lipsTogetherU.ty", 1.0 },
		{ "CTRL_R_mouth_lipsTogetherU.ty", 1.0 },
		{ "CTRL_L_mouth_lipsTogetherD.ty", 1.0 },
		{ "CTRL_R_mouth_lipsTogetherD.ty", 1.0 },
		{ "CTRL_L_mouth_lipBiteU.ty", 1.0 },
		{ "CTRL_R_mouth_lipBiteU.ty", 1.0 },
		{ "CTRL_L_mouth_lipBiteD.ty", 1.0 },
		{ "CTRL_R_mouth_lipBiteD.ty", 1.0 },
		{ "CTRL_L_mouth_tightenU.ty", 1.0 },
		{ "CTRL_R_mouth_tightenU.ty", 1.0 },
		{ "CTRL_L_mouth_tightenD.ty", 1.0 },
		{ "CTRL_R_mouth_tightenD.ty", 1.0 },
		{ "CTRL_L_mouth_lipsPressU.ty", 1.0 },
		{ "CTRL_R_mouth_lipsPressU.ty", 1.0 },
		{ "CTRL_L_mouth_sharpCornerPull.ty", 1.0 },
		{ "CTRL_R_mouth_sharpCornerPull.ty", 1.0 },
		{ "CTRL_C_mouth_stickyU.ty", 1.0 },
		{ "CTRL_L_mouth_stickyInnerU.ty", 1.0 },
		{ "CTRL_R_mouth_stickyInnerU.ty", 1.0 },
		{ "CTRL_L_mouth_stickyOuterU.ty", 1.0 },
		{ "CTRL_R_mouth_stickyOuterU.ty", 1.0 },
		{ "CTRL_C_mouth_stickyD.ty", 1.0 },
		{ "CTRL_L_mouth_stickyInnerD.ty", 1.0 },
		{ "CTRL_R_mouth_stickyInnerD.ty", 1.0 },
		{ "CTRL_L_mouth_stickyOuterD.ty", 1.0 },
		{ "CTRL_R_mouth_stickyOuterD.ty", 1.0 },
		{ "CTRL_L_mouth_lipSticky.ty", 1.0 },
		{ "CTRL_R_mouth_lipSticky.ty", 1.0 },
		{ "CTRL_L_mouth_pushPullU.ty", 1.0 },
		{ "CTRL_R_mouth_pushPullU.ty", 1.0 },
		{ "CTRL_L_mouth_pushPullD.ty", 1.0 },
		{ "CTRL_R_mouth_pushPullD.ty", 1.0 },
		{ "CTRL_L_mouth_thicknessU.ty", 1.0 },
		{ "CTRL_R_mouth_thicknessU.ty", 1.0 },
		{ "CTRL_L_mouth_thicknessD.ty", 1.0 },
		{ "CTRL_R_mouth_thicknessD.ty", 1.0 },
		{ "CTRL_L_mouth_thicknessInwardU.ty", 1.0 },
		{ "CTRL_R_mouth_thicknessInwardU.ty", 1.0 },
		{ "CTRL_L_mouth_thicknessInwardD.ty", 1.0 },
		{ "CTRL_R_mouth_thicknessInwardD.ty", 1.0 },
		{ "CTRL_L_mouth_cornerSharpnessU.ty", 1.0 },
		{ "CTRL_R_mouth_cornerSharpnessU.ty", 1.0 },
		{ "CTRL_L_mouth_cornerSharpnessD.ty", 1.0 },
		{ "CTRL_R_mouth_cornerSharpnessD.ty", 1.0 },
		{ "CTRL_L_mouth_lipsTowardsTeethU.ty", 1.0 },
		{ "CTRL_R_mouth_lipsTowardsTeethU.ty", 1.0 },
		{ "CTRL_L_mouth_lipsTowardsTeethD.ty", 1.0 },
		{ "CTRL_R_mouth_lipsTowardsTeethD.ty", 1.0 },
		{ "CTRL_C_mouth_lipShiftU.ty", 1.0 },
		{ "CTRL_C_mouth_lipShiftD.ty", 1.0 },
		{ "CTRL_L_mouth_lipsRollU.ty", 1.0 },
		{ "CTRL_R_mouth_lipsRollU.ty", 1.0 },
		{ "CTRL_L_mouth_lipsRollD.ty", 1.0 },
		{ "CTRL_R_mouth_lipsRollD.ty", 1.0 },
		{ "CTRL_L_mouth_corner.ty", 1.0 },
		{ "CTRL_L_mouth_corner.tx", 1.0 },
		{ "CTRL_R_mouth_corner.ty", 1.0 },
		{ "CTRL_R_mouth_corner.tx", 1.0 },
		{ "CTRL_C_tongue_inOut.ty", 1.0 },
		{ "CTRL_C_tongue_move.ty", 1.0 },
		{ "CTRL_C_tongue_move.tx", 1.0 },
		{ "CTRL_C_tongue_press.ty", 1.0 },
		{ "CTRL_C_tongue_wideNarrow.ty", 1.0 },
		{ "CTRL_C_tongue_bendTwist.ty", 1.0 },
		{ "CTRL_C_tongue_bendTwist.tx", 1.0 },
		{ "CTRL_C_tongue_roll.ty", 1.0 },
		{ "CTRL_C_tongue_tipMove.ty", 1.0 },
		{ "CTRL_C_tongue_tipMove.tx", 1.0 },
		{ "CTRL_C_tongue_thickThin.ty", 1.0 },
		{ "CTRL_C_jaw.ty", 1.0 },
		{ "CTRL_C_jaw.tx", 1.0 },
		{ "CTRL_C_jaw_fwdBack.ty", 1.0 },
		{ "CTRL_L_jaw_clench.ty", 1.0 },
		{ "CTRL_R_jaw_clench.ty", 1.0 },
		{ "CTRL_L_jaw_ChinRaiseU.ty", 1.0 },
		{ "CTRL_R_jaw_ChinRaiseU.ty", 1.0 },
		{ "CTRL_L_jaw_ChinRaiseD.ty", 1.0 },
		{ "CTRL_R_jaw_ChinRaiseD.ty", 1.0 },
		{ "CTRL_L_jaw_chinCompress.ty", 1.0 },
		{ "CTRL_R_jaw_chinCompress.ty", 1.0 },
		{ "CTRL_C_jaw_openExtreme.ty", 1.0 },
		{ "CTRL_L_neck_stretch.ty", 1.0 },
		{ "CTRL_R_neck_stretch.ty", 1.0 },
		{ "CTRL_C_neck_swallow.ty", 1.0 },
		{ "CTRL_L_neck_mastoidContract.ty", 1.0 },
		{ "CTRL_R_neck_mastoidContract.ty", 1.0 },
		{ "CTRL_neck_throatUpDown.ty", 1.0 },
		{ "CTRL_neck_digastricUpDown.ty", 1.0 },
		{ "CTRL_neck_throatExhaleInhale.ty", 1.0 },
		{ "CTRL_C_teethU.ty", 1.0 },
		{ "CTRL_C_teethU.tx", 1.0 },
		{ "CTRL_C_teeth_fwdBackU.ty", 1.0 },
		{ "CTRL_C_teethD.ty", 1.0 },
		{ "CTRL_C_teethD.tx", 1.0 },
		{ "CTRL_C_teeth_fwdBackD.ty", 1.0 },
	};

	const TMap<FString, float> ExpectedRigControls = {
		{ "CTRL_expressions_browDownL", 1 },
		{ "CTRL_expressions_browDownR", 1 },
		{ "CTRL_expressions_browLateralL", 1 },
		{ "CTRL_expressions_browLateralR", 1 },
		{ "CTRL_expressions_browRaiseInL", 1 },
		{ "CTRL_expressions_browRaiseInR", 1 },
		{ "CTRL_expressions_browRaiseOuterL", 1 },
		{ "CTRL_expressions_browRaiseOuterR", 1 },
		{ "CTRL_expressions_earUpL", 1 },
		{ "CTRL_expressions_earUpR", 1 },
		{ "CTRL_expressions_eyeBlinkL", 1 },
		{ "CTRL_expressions_eyeBlinkR", 1 },
		{ "CTRL_expressions_eyeCheekRaiseL", 1 },
		{ "CTRL_expressions_eyeCheekRaiseR", 1 },
		{ "CTRL_expressions_eyeFaceScrunchL", 1 },
		{ "CTRL_expressions_eyeFaceScrunchR", 1 },
		{ "CTRL_expressions_eyeLidPressL", 1 },
		{ "CTRL_expressions_eyeLidPressR", 1 },
		{ "CTRL_expressions_eyeLookDownL", 0 },
		{ "CTRL_expressions_eyeLookDownR", 0 },
		{ "CTRL_expressions_eyeLookLeftL", 1 },
		{ "CTRL_expressions_eyeLookLeftR", 1 },
		{ "CTRL_expressions_eyeLookRightL", 0 },
		{ "CTRL_expressions_eyeLookRightR", 0 },
		{ "CTRL_expressions_eyeLookUpL", 1 },
		{ "CTRL_expressions_eyeLookUpR", 1 },
		{ "CTRL_expressions_eyeLowerLidDownL", 0 },
		{ "CTRL_expressions_eyeLowerLidDownR", 0 },
		{ "CTRL_expressions_eyeLowerLidUpL", 1 },
		{ "CTRL_expressions_eyeLowerLidUpR", 1 },
		{ "CTRL_expressions_eyeParallelLookDirection", 1 },
		{ "CTRL_expressions_eyePupilNarrowL", 0 },
		{ "CTRL_expressions_eyePupilNarrowR", 0 },
		{ "CTRL_expressions_eyePupilWideL", 1 },
		{ "CTRL_expressions_eyePupilWideR", 1 },
		{ "CTRL_expressions_eyeRelaxL", 1 },
		{ "CTRL_expressions_eyeRelaxR", 1 },
		{ "CTRL_expressions_eyeSquintInnerL", 1 },
		{ "CTRL_expressions_eyeSquintInnerR", 1 },
		{ "CTRL_expressions_eyeUpperLidUpL", 0 },
		{ "CTRL_expressions_eyeUpperLidUpR", 0 },
		{ "CTRL_expressions_eyeWidenL", 0 },
		{ "CTRL_expressions_eyeWidenR", 0 },
		{ "CTRL_expressions_eyelashesDownINL", 1 },
		{ "CTRL_expressions_eyelashesDownINR", 1 },
		{ "CTRL_expressions_eyelashesDownOUTL", 1 },
		{ "CTRL_expressions_eyelashesDownOUTR", 1 },
		{ "CTRL_expressions_eyelashesUpINL", 0 },
		{ "CTRL_expressions_eyelashesUpINR", 0 },
		{ "CTRL_expressions_eyelashesUpOUTL", 0 },
		{ "CTRL_expressions_eyelashesUpOUTR", 0 },
		{ "CTRL_expressions_jawBack", 1 },
		{ "CTRL_expressions_jawChinCompressL", 1 },
		{ "CTRL_expressions_jawChinCompressR", 1 },
		{ "CTRL_expressions_jawChinRaiseDL", 1 },
		{ "CTRL_expressions_jawChinRaiseDR", 1 },
		{ "CTRL_expressions_jawChinRaiseUL", 1 },
		{ "CTRL_expressions_jawChinRaiseUR", 1 },
		{ "CTRL_expressions_jawClenchL", 1 },
		{ "CTRL_expressions_jawClenchR", 1 },
		{ "CTRL_expressions_jawFwd", 0 },
		{ "CTRL_expressions_jawLeft", 0 },
		{ "CTRL_expressions_jawOpen", 1 },
		{ "CTRL_expressions_jawOpenExtreme", 1 },
		{ "CTRL_expressions_jawRight", 1 },
		{ "CTRL_expressions_mouthCheekBlowL", 1 },
		{ "CTRL_expressions_mouthCheekBlowR", 1 },
		{ "CTRL_expressions_mouthCheekSuckL", 0 },
		{ "CTRL_expressions_mouthCheekSuckR", 0 },
		{ "CTRL_expressions_mouthCornerDepressL", 1 },
		{ "CTRL_expressions_mouthCornerDepressR", 1 },
		{ "CTRL_expressions_mouthCornerDownL", 0 },
		{ "CTRL_expressions_mouthCornerDownR", 0 },
		{ "CTRL_expressions_mouthCornerNarrowL", 0 },
		{ "CTRL_expressions_mouthCornerNarrowR", 0 },
		{ "CTRL_expressions_mouthCornerPullL", 1 },
		{ "CTRL_expressions_mouthCornerPullR", 1 },
		{ "CTRL_expressions_mouthCornerRounderDL", 0 },
		{ "CTRL_expressions_mouthCornerRounderDR", 0 },
		{ "CTRL_expressions_mouthCornerRounderUL", 0 },
		{ "CTRL_expressions_mouthCornerRounderUR", 0 },
		{ "CTRL_expressions_mouthCornerSharpenDL", 1 },
		{ "CTRL_expressions_mouthCornerSharpenDR", 1 },
		{ "CTRL_expressions_mouthCornerSharpenUL", 1 },
		{ "CTRL_expressions_mouthCornerSharpenUR", 1 },
		{ "CTRL_expressions_mouthCornerUpL", 1 },
		{ "CTRL_expressions_mouthCornerUpR", 1 },
		{ "CTRL_expressions_mouthCornerWideL", 1 },
		{ "CTRL_expressions_mouthCornerWideR", 1 },
		{ "CTRL_expressions_mouthDimpleL", 1 },
		{ "CTRL_expressions_mouthDimpleR", 1 },
		{ "CTRL_expressions_mouthDown", 0 },
		{ "CTRL_expressions_mouthFunnelDL", 1 },
		{ "CTRL_expressions_mouthFunnelDR", 1 },
		{ "CTRL_expressions_mouthFunnelUL", 1 },
		{ "CTRL_expressions_mouthFunnelUR", 1 },
		{ "CTRL_expressions_mouthLeft", 1 },
		{ "CTRL_expressions_mouthLipsBlowL", 1 },
		{ "CTRL_expressions_mouthLipsBlowR", 1 },
		{ "CTRL_expressions_mouthLipsPressL", 1 },
		{ "CTRL_expressions_mouthLipsPressR", 1 },
		{ "CTRL_expressions_mouthLipsPullDL", 0 },
		{ "CTRL_expressions_mouthLipsPullDR", 0 },
		{ "CTRL_expressions_mouthLipsPullUL", 0 },
		{ "CTRL_expressions_mouthLipsPullUR", 0 },
		{ "CTRL_expressions_mouthLipsPurseDL", 1 },
		{ "CTRL_expressions_mouthLipsPurseDR", 1 },
		{ "CTRL_expressions_mouthLipsPurseUL", 1 },
		{ "CTRL_expressions_mouthLipsPurseUR", 1 },
		{ "CTRL_expressions_mouthLipsPushDL", 1 },
		{ "CTRL_expressions_mouthLipsPushDR", 1 },
		{ "CTRL_expressions_mouthLipsPushUL", 1 },
		{ "CTRL_expressions_mouthLipsPushUR", 1 },
		{ "CTRL_expressions_mouthLipsStickyLPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyLPh3", 1 },
		{ "CTRL_expressions_mouthLipsStickyRPh1", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh2", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh3", 1 },
		{ "CTRL_expressions_mouthLipsThickDL", 0 },
		{ "CTRL_expressions_mouthLipsThickDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUR", 0 },
		{ "CTRL_expressions_mouthLipsThickUL", 0 },
		{ "CTRL_expressions_mouthLipsThickUR", 0 },
		{ "CTRL_expressions_mouthLipsThinDL", 1 },
		{ "CTRL_expressions_mouthLipsThinDR", 1 },
		{ "CTRL_expressions_mouthLipsThinInwardDL", 1 },
		{ "CTRL_expressions_mouthLipsThinInwardDR", 1 },
		{ "CTRL_expressions_mouthLipsThinInwardUL", 1 },
		{ "CTRL_expressions_mouthLipsThinInwardUR", 1 },
		{ "CTRL_expressions_mouthLipsThinUL", 1 },
		{ "CTRL_expressions_mouthLipsThinUR", 1 },
		{ "CTRL_expressions_mouthLipsTightenDL", 1 },
		{ "CTRL_expressions_mouthLipsTightenDR", 1 },
		{ "CTRL_expressions_mouthLipsTightenUL", 1 },
		{ "CTRL_expressions_mouthLipsTightenUR", 1 },
		{ "CTRL_expressions_mouthLipsTogetherDL", 1 },
		{ "CTRL_expressions_mouthLipsTogetherDR", 1 },
		{ "CTRL_expressions_mouthLipsTogetherUL", 1 },
		{ "CTRL_expressions_mouthLipsTogetherUR", 1 },
		{ "CTRL_expressions_mouthLipsTowardsDL", 1 },
		{ "CTRL_expressions_mouthLipsTowardsDR", 1 },
		{ "CTRL_expressions_mouthLipsTowardsUL", 1 },
		{ "CTRL_expressions_mouthLipsTowardsUR", 1 },
		{ "CTRL_expressions_mouthLowerLipBiteL", 1 },
		{ "CTRL_expressions_mouthLowerLipBiteR", 1 },
		{ "CTRL_expressions_mouthLowerLipDepressL", 1 },
		{ "CTRL_expressions_mouthLowerLipDepressR", 1 },
		{ "CTRL_expressions_mouthLowerLipRollInL", 1 },
		{ "CTRL_expressions_mouthLowerLipRollInR", 1 },
		{ "CTRL_expressions_mouthLowerLipRollOutL", 0 },
		{ "CTRL_expressions_mouthLowerLipRollOutR", 0 },
		{ "CTRL_expressions_mouthLowerLipShiftLeft", 1 },
		{ "CTRL_expressions_mouthLowerLipShiftRight", 0 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethL", 1 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethR", 1 },
		{ "CTRL_expressions_mouthPressDL", 1 },
		{ "CTRL_expressions_mouthPressDR", 1 },
		{ "CTRL_expressions_mouthPressUL", 1 },
		{ "CTRL_expressions_mouthPressUR", 1 },
		{ "CTRL_expressions_mouthRight", 0 },
		{ "CTRL_expressions_mouthSharpCornerPullL", 1 },
		{ "CTRL_expressions_mouthSharpCornerPullR", 1 },
		{ "CTRL_expressions_mouthStickyDC", 1 },
		{ "CTRL_expressions_mouthStickyDINL", 1 },
		{ "CTRL_expressions_mouthStickyDINR", 1 },
		{ "CTRL_expressions_mouthStickyDOUTL", 1 },
		{ "CTRL_expressions_mouthStickyDOUTR", 1 },
		{ "CTRL_expressions_mouthStickyUC", 1 },
		{ "CTRL_expressions_mouthStickyUINL", 1 },
		{ "CTRL_expressions_mouthStickyUINR", 1 },
		{ "CTRL_expressions_mouthStickyUOUTL", 1 },
		{ "CTRL_expressions_mouthStickyUOUTR", 1 },
		{ "CTRL_expressions_mouthStretchL", 1 },
		{ "CTRL_expressions_mouthStretchLipsCloseL", 1 },
		{ "CTRL_expressions_mouthStretchLipsCloseR", 1 },
		{ "CTRL_expressions_mouthStretchR", 1 },
		{ "CTRL_expressions_mouthUp", 1 },
		{ "CTRL_expressions_mouthUpperLipBiteL", 1 },
		{ "CTRL_expressions_mouthUpperLipBiteR", 1 },
		{ "CTRL_expressions_mouthUpperLipRaiseL", 1 },
		{ "CTRL_expressions_mouthUpperLipRaiseR", 1 },
		{ "CTRL_expressions_mouthUpperLipRollInL", 1 },
		{ "CTRL_expressions_mouthUpperLipRollInR", 1 },
		{ "CTRL_expressions_mouthUpperLipRollOutL", 0 },
		{ "CTRL_expressions_mouthUpperLipRollOutR", 0 },
		{ "CTRL_expressions_mouthUpperLipShiftLeft", 1 },
		{ "CTRL_expressions_mouthUpperLipShiftRight", 0 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethL", 1 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethR", 1 },
		{ "CTRL_expressions_neckDigastricDown", 0 },
		{ "CTRL_expressions_neckDigastricUp", 1 },
		{ "CTRL_expressions_neckMastoidContractL", 1 },
		{ "CTRL_expressions_neckMastoidContractR", 1 },
		{ "CTRL_expressions_neckStretchL", 1 },
		{ "CTRL_expressions_neckStretchR", 1 },
		{ "CTRL_expressions_neckSwallowPh1", 0 },
		{ "CTRL_expressions_neckSwallowPh2", 0 },
		{ "CTRL_expressions_neckSwallowPh3", 0 },
		{ "CTRL_expressions_neckSwallowPh4", 0 },
		{ "CTRL_expressions_neckThroatDown", 0 },
		{ "CTRL_expressions_neckThroatExhale", 0 },
		{ "CTRL_expressions_neckThroatInhale", 1 },
		{ "CTRL_expressions_neckThroatUp", 1 },
		{ "CTRL_expressions_noseNasolabialDeepenL", 1 },
		{ "CTRL_expressions_noseNasolabialDeepenR", 1 },
		{ "CTRL_expressions_noseNostrilCompressL", 0 },
		{ "CTRL_expressions_noseNostrilCompressR", 0 },
		{ "CTRL_expressions_noseNostrilDepressL", 0 },
		{ "CTRL_expressions_noseNostrilDepressR", 0 },
		{ "CTRL_expressions_noseNostrilDilateL", 1 },
		{ "CTRL_expressions_noseNostrilDilateR", 1 },
		{ "CTRL_expressions_noseWrinkleL", 1 },
		{ "CTRL_expressions_noseWrinkleR", 1 },
		{ "CTRL_expressions_noseWrinkleUpperL", 1 },
		{ "CTRL_expressions_noseWrinkleUpperR", 1 },
		{ "CTRL_expressions_teethBackD", 1 },
		{ "CTRL_expressions_teethBackU", 1 },
		{ "CTRL_expressions_teethDownD", 0 },
		{ "CTRL_expressions_teethDownU", 0 },
		{ "CTRL_expressions_teethFwdD", 0 },
		{ "CTRL_expressions_teethFwdU", 0 },
		{ "CTRL_expressions_teethLeftD", 1 },
		{ "CTRL_expressions_teethLeftU", 1 },
		{ "CTRL_expressions_teethRightD", 0 },
		{ "CTRL_expressions_teethRightU", 0 },
		{ "CTRL_expressions_teethUpD", 1 },
		{ "CTRL_expressions_teethUpU", 1 },
		{ "CTRL_expressions_tongueBendDown", 0 },
		{ "CTRL_expressions_tongueBendUp", 1 },
		{ "CTRL_expressions_tongueDown", 0 },
		{ "CTRL_expressions_tongueIn", 1 },
		{ "CTRL_expressions_tongueLeft", 1 },
		{ "CTRL_expressions_tongueNarrow", 0 },
		{ "CTRL_expressions_tongueOut", 0 },
		{ "CTRL_expressions_tonguePress", 1 },
		{ "CTRL_expressions_tongueRight", 0 },
		{ "CTRL_expressions_tongueRoll", 1 },
		{ "CTRL_expressions_tongueThick", 1 },
		{ "CTRL_expressions_tongueThin", 0 },
		{ "CTRL_expressions_tongueTipDown", 0 },
		{ "CTRL_expressions_tongueTipLeft", 1 },
		{ "CTRL_expressions_tongueTipRight", 0 },
		{ "CTRL_expressions_tongueTipUp", 1 },
		{ "CTRL_expressions_tongueTwistLeft", 1 },
		{ "CTRL_expressions_tongueTwistRight", 0 },
		{ "CTRL_expressions_tongueUp", 1 },
		{ "CTRL_expressions_tongueWide", 1 }
	};
}

namespace ControlsHalfTestData
{
	const TMap<FString, float> InputSolveControls = {
		{ "CTRL_L_brow_down.ty", 0.5 },
		{ "CTRL_R_brow_down.ty", 0.5 },
		{ "CTRL_L_brow_lateral.ty", 0.5 },
		{ "CTRL_R_brow_lateral.ty", 0.5 },
		{ "CTRL_L_brow_raiseIn.ty", 0.5 },
		{ "CTRL_R_brow_raiseIn.ty", 0.5 },
		{ "CTRL_L_brow_raiseOut.ty", 0.5 },
		{ "CTRL_R_brow_raiseOut.ty", 0.5 },
		{ "CTRL_L_ear_up.ty", 0.5 },
		{ "CTRL_R_ear_up.ty", 0.5 },
		{ "CTRL_L_eye_blink.ty", 0.5 },
		{ "CTRL_R_eye_blink.ty", 0.5 },
		{ "CTRL_L_eye_lidPress.ty", 0.5 },
		{ "CTRL_R_eye_lidPress.ty", 0.5 },
		{ "CTRL_L_eye_squintInner.ty", 0.5 },
		{ "CTRL_R_eye_squintInner.ty", 0.5 },
		{ "CTRL_L_eye_cheekRaise.ty", 0.5 },
		{ "CTRL_R_eye_cheekRaise.ty", 0.5 },
		{ "CTRL_L_eye_faceScrunch.ty", 0.5 },
		{ "CTRL_R_eye_faceScrunch.ty", 0.5 },
		{ "CTRL_L_eye_eyelidU.ty", 0.5 },
		{ "CTRL_R_eye_eyelidU.ty", 0.5 },
		{ "CTRL_L_eye_eyelidD.ty", 0.5 },
		{ "CTRL_R_eye_eyelidD.ty", 0.5 },
		{ "CTRL_L_eye.ty", 0.5 },
		{ "CTRL_R_eye.ty", 0.5 },
		{ "CTRL_L_eye.tx", 0.5 },
		{ "CTRL_R_eye.tx", 0.5 },
		{ "CTRL_L_eye_pupil.ty", 0.5 },
		{ "CTRL_R_eye_pupil.ty", 0.5 },
		{ "CTRL_C_eye_parallelLook.ty", 0.5 },
		{ "CTRL_L_eyelashes_tweakerIn.ty", 0.5 },
		{ "CTRL_R_eyelashes_tweakerIn.ty", 0.5 },
		{ "CTRL_L_eyelashes_tweakerOut.ty", 0.5 },
		{ "CTRL_R_eyelashes_tweakerOut.ty", 0.5 },
		{ "CTRL_L_nose.ty", 0.5 },
		{ "CTRL_R_nose.ty", 0.5 },
		{ "CTRL_L_nose.tx", 0.5 },
		{ "CTRL_R_nose.tx", 0.5 },
		{ "CTRL_L_nose_wrinkleUpper.ty", 0.5 },
		{ "CTRL_R_nose_wrinkleUpper.ty", 0.5 },
		{ "CTRL_L_nose_nasolabialDeepen.ty", 0.5 },
		{ "CTRL_R_nose_nasolabialDeepen.ty", 0.5 },
		{ "CTRL_L_mouth_suckBlow.ty", 0.5 },
		{ "CTRL_R_mouth_suckBlow.ty", 0.5 },
		{ "CTRL_L_mouth_lipsBlow.ty", 0.5 },
		{ "CTRL_R_mouth_lipsBlow.ty", 0.5 },
		{ "CTRL_C_mouth.ty", 0.5 },
		{ "CTRL_C_mouth.tx", 0.5 },
		{ "CTRL_L_mouth_upperLipRaise.ty", 0.5 },
		{ "CTRL_R_mouth_upperLipRaise.ty", 0.5 },
		{ "CTRL_L_mouth_lowerLipDepress.ty", 0.5 },
		{ "CTRL_R_mouth_lowerLipDepress.ty", 0.5 },
		{ "CTRL_L_mouth_cornerPull.ty", 0.5 },
		{ "CTRL_R_mouth_cornerPull.ty", 0.5 },
		{ "CTRL_L_mouth_stretch.ty", 0.5 },
		{ "CTRL_R_mouth_stretch.ty", 0.5 },
		{ "CTRL_L_mouth_stretchLipsClose.ty", 0.5 },
		{ "CTRL_R_mouth_stretchLipsClose.ty", 0.5 },
		{ "CTRL_L_mouth_dimple.ty", 0.5 },
		{ "CTRL_R_mouth_dimple.ty", 0.5 },
		{ "CTRL_L_mouth_cornerDepress.ty", 0.5 },
		{ "CTRL_R_mouth_cornerDepress.ty", 0.5 },
		{ "CTRL_L_mouth_pressU.ty", 0.5 },
		{ "CTRL_R_mouth_pressU.ty", 0.5 },
		{ "CTRL_L_mouth_pressD.ty", 0.5 },
		{ "CTRL_R_mouth_pressD.ty", 0.5 },
		{ "CTRL_L_mouth_purseU.ty", 0.5 },
		{ "CTRL_R_mouth_purseU.ty", 0.5 },
		{ "CTRL_L_mouth_purseD.ty", 0.5 },
		{ "CTRL_R_mouth_purseD.ty", 0.5 },
		{ "CTRL_L_mouth_towardsU.ty", 0.5 },
		{ "CTRL_R_mouth_towardsU.ty", 0.5 },
		{ "CTRL_L_mouth_towardsD.ty", 0.5 },
		{ "CTRL_R_mouth_towardsD.ty", 0.5 },
		{ "CTRL_L_mouth_funnelU.ty", 0.5 },
		{ "CTRL_R_mouth_funnelU.ty", 0.5 },
		{ "CTRL_L_mouth_funnelD.ty", 0.5 },
		{ "CTRL_R_mouth_funnelD.ty", 0.5 },
		{ "CTRL_L_mouth_lipsTogetherU.ty", 0.5 },
		{ "CTRL_R_mouth_lipsTogetherU.ty", 0.5 },
		{ "CTRL_L_mouth_lipsTogetherD.ty", 0.5 },
		{ "CTRL_R_mouth_lipsTogetherD.ty", 0.5 },
		{ "CTRL_L_mouth_lipBiteU.ty", 0.5 },
		{ "CTRL_R_mouth_lipBiteU.ty", 0.5 },
		{ "CTRL_L_mouth_lipBiteD.ty", 0.5 },
		{ "CTRL_R_mouth_lipBiteD.ty", 0.5 },
		{ "CTRL_L_mouth_tightenU.ty", 0.5 },
		{ "CTRL_R_mouth_tightenU.ty", 0.5 },
		{ "CTRL_L_mouth_tightenD.ty", 0.5 },
		{ "CTRL_R_mouth_tightenD.ty", 0.5 },
		{ "CTRL_L_mouth_lipsPressU.ty", 0.5 },
		{ "CTRL_R_mouth_lipsPressU.ty", 0.5 },
		{ "CTRL_L_mouth_sharpCornerPull.ty", 0.5 },
		{ "CTRL_R_mouth_sharpCornerPull.ty", 0.5 },
		{ "CTRL_C_mouth_stickyU.ty", 0.5 },
		{ "CTRL_L_mouth_stickyInnerU.ty", 0.5 },
		{ "CTRL_R_mouth_stickyInnerU.ty", 0.5 },
		{ "CTRL_L_mouth_stickyOuterU.ty", 0.5 },
		{ "CTRL_R_mouth_stickyOuterU.ty", 0.5 },
		{ "CTRL_C_mouth_stickyD.ty", 0.5 },
		{ "CTRL_L_mouth_stickyInnerD.ty", 0.5 },
		{ "CTRL_R_mouth_stickyInnerD.ty", 0.5 },
		{ "CTRL_L_mouth_stickyOuterD.ty", 0.5 },
		{ "CTRL_R_mouth_stickyOuterD.ty", 0.5 },
		{ "CTRL_L_mouth_lipSticky.ty", 0.5 },
		{ "CTRL_R_mouth_lipSticky.ty", 0.5 },
		{ "CTRL_L_mouth_pushPullU.ty", 0.5 },
		{ "CTRL_R_mouth_pushPullU.ty", 0.5 },
		{ "CTRL_L_mouth_pushPullD.ty", 0.5 },
		{ "CTRL_R_mouth_pushPullD.ty", 0.5 },
		{ "CTRL_L_mouth_thicknessU.ty", 0.5 },
		{ "CTRL_R_mouth_thicknessU.ty", 0.5 },
		{ "CTRL_L_mouth_thicknessD.ty", 0.5 },
		{ "CTRL_R_mouth_thicknessD.ty", 0.5 },
		{ "CTRL_L_mouth_thicknessInwardU.ty", 0.5 },
		{ "CTRL_R_mouth_thicknessInwardU.ty", 0.5 },
		{ "CTRL_L_mouth_thicknessInwardD.ty", 0.5 },
		{ "CTRL_R_mouth_thicknessInwardD.ty", 0.5 },
		{ "CTRL_L_mouth_cornerSharpnessU.ty", 0.5 },
		{ "CTRL_R_mouth_cornerSharpnessU.ty", 0.5 },
		{ "CTRL_L_mouth_cornerSharpnessD.ty", 0.5 },
		{ "CTRL_R_mouth_cornerSharpnessD.ty", 0.5 },
		{ "CTRL_L_mouth_lipsTowardsTeethU.ty", 0.5 },
		{ "CTRL_R_mouth_lipsTowardsTeethU.ty", 0.5 },
		{ "CTRL_L_mouth_lipsTowardsTeethD.ty", 0.5 },
		{ "CTRL_R_mouth_lipsTowardsTeethD.ty", 0.5 },
		{ "CTRL_C_mouth_lipShiftU.ty", 0.5 },
		{ "CTRL_C_mouth_lipShiftD.ty", 0.5 },
		{ "CTRL_L_mouth_lipsRollU.ty", 0.5 },
		{ "CTRL_R_mouth_lipsRollU.ty", 0.5 },
		{ "CTRL_L_mouth_lipsRollD.ty", 0.5 },
		{ "CTRL_R_mouth_lipsRollD.ty", 0.5 },
		{ "CTRL_L_mouth_corner.ty", 0.5 },
		{ "CTRL_L_mouth_corner.tx", 0.5 },
		{ "CTRL_R_mouth_corner.ty", 0.5 },
		{ "CTRL_R_mouth_corner.tx", 0.5 },
		{ "CTRL_C_tongue_inOut.ty", 0.5 },
		{ "CTRL_C_tongue_move.ty", 0.5 },
		{ "CTRL_C_tongue_move.tx", 0.5 },
		{ "CTRL_C_tongue_press.ty", 0.5 },
		{ "CTRL_C_tongue_wideNarrow.ty", 0.5 },
		{ "CTRL_C_tongue_bendTwist.ty", 0.5 },
		{ "CTRL_C_tongue_bendTwist.tx", 0.5 },
		{ "CTRL_C_tongue_roll.ty", 0.5 },
		{ "CTRL_C_tongue_tipMove.ty", 0.5 },
		{ "CTRL_C_tongue_tipMove.tx", 0.5 },
		{ "CTRL_C_tongue_thickThin.ty", 0.5 },
		{ "CTRL_C_jaw.ty", 0.5 },
		{ "CTRL_C_jaw.tx", 0.5 },
		{ "CTRL_C_jaw_fwdBack.ty", 0.5 },
		{ "CTRL_L_jaw_clench.ty", 0.5 },
		{ "CTRL_R_jaw_clench.ty", 0.5 },
		{ "CTRL_L_jaw_ChinRaiseU.ty", 0.5 },
		{ "CTRL_R_jaw_ChinRaiseU.ty", 0.5 },
		{ "CTRL_L_jaw_ChinRaiseD.ty", 0.5 },
		{ "CTRL_R_jaw_ChinRaiseD.ty", 0.5 },
		{ "CTRL_L_jaw_chinCompress.ty", 0.5 },
		{ "CTRL_R_jaw_chinCompress.ty", 0.5 },
		{ "CTRL_C_jaw_openExtreme.ty", 0.5 },
		{ "CTRL_L_neck_stretch.ty", 0.5 },
		{ "CTRL_R_neck_stretch.ty", 0.5 },
		{ "CTRL_C_neck_swallow.ty", 0.5 },
		{ "CTRL_L_neck_mastoidContract.ty", 0.5 },
		{ "CTRL_R_neck_mastoidContract.ty", 0.5 },
		{ "CTRL_neck_throatUpDown.ty", 0.5 },
		{ "CTRL_neck_digastricUpDown.ty", 0.5 },
		{ "CTRL_neck_throatExhaleInhale.ty", 0.5 },
		{ "CTRL_C_teethU.ty", 0.5 },
		{ "CTRL_C_teethU.tx", 0.5 },
		{ "CTRL_C_teeth_fwdBackU.ty", 0.5 },
		{ "CTRL_C_teethD.ty", 0.5 },
		{ "CTRL_C_teethD.tx", 0.5 },
		{ "CTRL_C_teeth_fwdBackD.ty", 0.5 }
	};

	const TMap<FString, float> ExpectedRigControls = {
		{ "CTRL_expressions_browDownL", 0.5 },
		{ "CTRL_expressions_browDownR", 0.5 },
		{ "CTRL_expressions_browLateralL", 0.5 },
		{ "CTRL_expressions_browLateralR", 0.5 },
		{ "CTRL_expressions_browRaiseInL", 0.5 },
		{ "CTRL_expressions_browRaiseInR", 0.5 },
		{ "CTRL_expressions_browRaiseOuterL", 0.5 },
		{ "CTRL_expressions_browRaiseOuterR", 0.5 },
		{ "CTRL_expressions_earUpL", 0.5 },
		{ "CTRL_expressions_earUpR", 0.5 },
		{ "CTRL_expressions_eyeBlinkL", 0.5 },
		{ "CTRL_expressions_eyeBlinkR", 0.5 },
		{ "CTRL_expressions_eyeCheekRaiseL", 0.5 },
		{ "CTRL_expressions_eyeCheekRaiseR", 0.5 },
		{ "CTRL_expressions_eyeFaceScrunchL", 0.5 },
		{ "CTRL_expressions_eyeFaceScrunchR", 0.5 },
		{ "CTRL_expressions_eyeLidPressL", 0.5 },
		{ "CTRL_expressions_eyeLidPressR", 0.5 },
		{ "CTRL_expressions_eyeLookDownL", 0 },
		{ "CTRL_expressions_eyeLookDownR", 0 },
		{ "CTRL_expressions_eyeLookLeftL", 0.5 },
		{ "CTRL_expressions_eyeLookLeftR", 0.5 },
		{ "CTRL_expressions_eyeLookRightL", 0 },
		{ "CTRL_expressions_eyeLookRightR", 0 },
		{ "CTRL_expressions_eyeLookUpL", 0.5 },
		{ "CTRL_expressions_eyeLookUpR", 0.5 },
		{ "CTRL_expressions_eyeLowerLidDownL", 0 },
		{ "CTRL_expressions_eyeLowerLidDownR", 0 },
		{ "CTRL_expressions_eyeLowerLidUpL", 0.5 },
		{ "CTRL_expressions_eyeLowerLidUpR", 0.5 },
		{ "CTRL_expressions_eyeParallelLookDirection", 0.5 },
		{ "CTRL_expressions_eyePupilNarrowL", 0 },
		{ "CTRL_expressions_eyePupilNarrowR", 0 },
		{ "CTRL_expressions_eyePupilWideL", 0.5 },
		{ "CTRL_expressions_eyePupilWideR", 0.5 },
		{ "CTRL_expressions_eyeRelaxL", 0.5 },
		{ "CTRL_expressions_eyeRelaxR", 0.5 },
		{ "CTRL_expressions_eyeSquintInnerL", 0.5 },
		{ "CTRL_expressions_eyeSquintInnerR", 0.5 },
		{ "CTRL_expressions_eyeUpperLidUpL", 0 },
		{ "CTRL_expressions_eyeUpperLidUpR", 0 },
		{ "CTRL_expressions_eyeWidenL", 0 },
		{ "CTRL_expressions_eyeWidenR", 0 },
		{ "CTRL_expressions_eyelashesDownINL", 0.5 },
		{ "CTRL_expressions_eyelashesDownINR", 0.5 },
		{ "CTRL_expressions_eyelashesDownOUTL", 0.5 },
		{ "CTRL_expressions_eyelashesDownOUTR", 0.5 },
		{ "CTRL_expressions_eyelashesUpINL", 0 },
		{ "CTRL_expressions_eyelashesUpINR", 0 },
		{ "CTRL_expressions_eyelashesUpOUTL", 0 },
		{ "CTRL_expressions_eyelashesUpOUTR", 0 },
		{ "CTRL_expressions_jawBack", 0.5 },
		{ "CTRL_expressions_jawChinCompressL", 0.5 },
		{ "CTRL_expressions_jawChinCompressR", 0.5 },
		{ "CTRL_expressions_jawChinRaiseDL", 0.5 },
		{ "CTRL_expressions_jawChinRaiseDR", 0.5 },
		{ "CTRL_expressions_jawChinRaiseUL", 0.5 },
		{ "CTRL_expressions_jawChinRaiseUR", 0.5 },
		{ "CTRL_expressions_jawClenchL", 0.5 },
		{ "CTRL_expressions_jawClenchR", 0.5 },
		{ "CTRL_expressions_jawFwd", 0 },
		{ "CTRL_expressions_jawLeft", 0 },
		{ "CTRL_expressions_jawOpen", 0.5 },
		{ "CTRL_expressions_jawOpenExtreme", 0.5 },
		{ "CTRL_expressions_jawRight", 0.5 },
		{ "CTRL_expressions_mouthCheekBlowL", 0.5 },
		{ "CTRL_expressions_mouthCheekBlowR", 0.5 },
		{ "CTRL_expressions_mouthCheekSuckL", 0 },
		{ "CTRL_expressions_mouthCheekSuckR", 0 },
		{ "CTRL_expressions_mouthCornerDepressL", 0.5 },
		{ "CTRL_expressions_mouthCornerDepressR", 0.5 },
		{ "CTRL_expressions_mouthCornerDownL", 0 },
		{ "CTRL_expressions_mouthCornerDownR", 0 },
		{ "CTRL_expressions_mouthCornerNarrowL", 0 },
		{ "CTRL_expressions_mouthCornerNarrowR", 0 },
		{ "CTRL_expressions_mouthCornerPullL", 0.5 },
		{ "CTRL_expressions_mouthCornerPullR", 0.5 },
		{ "CTRL_expressions_mouthCornerRounderDL", 0 },
		{ "CTRL_expressions_mouthCornerRounderDR", 0 },
		{ "CTRL_expressions_mouthCornerRounderUL", 0 },
		{ "CTRL_expressions_mouthCornerRounderUR", 0 },
		{ "CTRL_expressions_mouthCornerSharpenDL", 0.5 },
		{ "CTRL_expressions_mouthCornerSharpenDR", 0.5 },
		{ "CTRL_expressions_mouthCornerSharpenUL", 0.5 },
		{ "CTRL_expressions_mouthCornerSharpenUR", 0.5 },
		{ "CTRL_expressions_mouthCornerUpL", 0.5 },
		{ "CTRL_expressions_mouthCornerUpR", 0.5 },
		{ "CTRL_expressions_mouthCornerWideL", 0.5 },
		{ "CTRL_expressions_mouthCornerWideR", 0.5 },
		{ "CTRL_expressions_mouthDimpleL", 0.5 },
		{ "CTRL_expressions_mouthDimpleR", 0.5 },
		{ "CTRL_expressions_mouthDown", 0 },
		{ "CTRL_expressions_mouthFunnelDL", 0.5 },
		{ "CTRL_expressions_mouthFunnelDR", 0.5 },
		{ "CTRL_expressions_mouthFunnelUL", 0.5 },
		{ "CTRL_expressions_mouthFunnelUR", 0.5 },
		{ "CTRL_expressions_mouthLeft", 0.5 },
		{ "CTRL_expressions_mouthLipsBlowL", 0.5 },
		{ "CTRL_expressions_mouthLipsBlowR", 0.5 },
		{ "CTRL_expressions_mouthLipsPressL", 0.5 },
		{ "CTRL_expressions_mouthLipsPressR", 0.5 },
		{ "CTRL_expressions_mouthLipsPullDL", 0 },
		{ "CTRL_expressions_mouthLipsPullDR", 0 },
		{ "CTRL_expressions_mouthLipsPullUL", 0 },
		{ "CTRL_expressions_mouthLipsPullUR", 0 },
		{ "CTRL_expressions_mouthLipsPurseDL", 0.5 },
		{ "CTRL_expressions_mouthLipsPurseDR", 0.5 },
		{ "CTRL_expressions_mouthLipsPurseUL", 0.5 },
		{ "CTRL_expressions_mouthLipsPurseUR", 0.5 },
		{ "CTRL_expressions_mouthLipsPushDL", 0.5 },
		{ "CTRL_expressions_mouthLipsPushDR", 0.5 },
		{ "CTRL_expressions_mouthLipsPushUL", 0.5 },
		{ "CTRL_expressions_mouthLipsPushUR", 0.5 },
		{ "CTRL_expressions_mouthLipsStickyLPh1", 0.484848 },
		{ "CTRL_expressions_mouthLipsStickyLPh2", 0.515152 },
		{ "CTRL_expressions_mouthLipsStickyLPh3", 0 },
		{ "CTRL_expressions_mouthLipsStickyRPh1", 0.484848 },
		{ "CTRL_expressions_mouthLipsStickyRPh2", 0.515152 },
		{ "CTRL_expressions_mouthLipsStickyRPh3", 0 },
		{ "CTRL_expressions_mouthLipsThickDL", 0 },
		{ "CTRL_expressions_mouthLipsThickDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardDR", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUL", 0 },
		{ "CTRL_expressions_mouthLipsThickInwardUR", 0 },
		{ "CTRL_expressions_mouthLipsThickUL", 0 },
		{ "CTRL_expressions_mouthLipsThickUR", 0 },
		{ "CTRL_expressions_mouthLipsThinDL", 0.5 },
		{ "CTRL_expressions_mouthLipsThinDR", 0.5 },
		{ "CTRL_expressions_mouthLipsThinInwardDL", 0.5 },
		{ "CTRL_expressions_mouthLipsThinInwardDR", 0.5 },
		{ "CTRL_expressions_mouthLipsThinInwardUL", 0.5 },
		{ "CTRL_expressions_mouthLipsThinInwardUR", 0.5 },
		{ "CTRL_expressions_mouthLipsThinUL", 0.5 },
		{ "CTRL_expressions_mouthLipsThinUR", 0.5 },
		{ "CTRL_expressions_mouthLipsTightenDL", 0.5 },
		{ "CTRL_expressions_mouthLipsTightenDR", 0.5 },
		{ "CTRL_expressions_mouthLipsTightenUL", 0.5 },
		{ "CTRL_expressions_mouthLipsTightenUR", 0.5 },
		{ "CTRL_expressions_mouthLipsTogetherDL", 0.5 },
		{ "CTRL_expressions_mouthLipsTogetherDR", 0.5 },
		{ "CTRL_expressions_mouthLipsTogetherUL", 0.5 },
		{ "CTRL_expressions_mouthLipsTogetherUR", 0.5 },
		{ "CTRL_expressions_mouthLipsTowardsDL", 0.5 },
		{ "CTRL_expressions_mouthLipsTowardsDR", 0.5 },
		{ "CTRL_expressions_mouthLipsTowardsUL", 0.5 },
		{ "CTRL_expressions_mouthLipsTowardsUR", 0.5 },
		{ "CTRL_expressions_mouthLowerLipBiteL", 0.5 },
		{ "CTRL_expressions_mouthLowerLipBiteR", 0.5 },
		{ "CTRL_expressions_mouthLowerLipDepressL", 0.5 },
		{ "CTRL_expressions_mouthLowerLipDepressR", 0.5 },
		{ "CTRL_expressions_mouthLowerLipRollInL", 0.5 },
		{ "CTRL_expressions_mouthLowerLipRollInR", 0.5 },
		{ "CTRL_expressions_mouthLowerLipRollOutL", 0 },
		{ "CTRL_expressions_mouthLowerLipRollOutR", 0 },
		{ "CTRL_expressions_mouthLowerLipShiftLeft", 0.5 },
		{ "CTRL_expressions_mouthLowerLipShiftRight", 0 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethL", 0.5 },
		{ "CTRL_expressions_mouthLowerLipTowardsTeethR", 0.5 },
		{ "CTRL_expressions_mouthPressDL", 0.5 },
		{ "CTRL_expressions_mouthPressDR", 0.5 },
		{ "CTRL_expressions_mouthPressUL", 0.5 },
		{ "CTRL_expressions_mouthPressUR", 0.5 },
		{ "CTRL_expressions_mouthRight", 0 },
		{ "CTRL_expressions_mouthSharpCornerPullL", 0.5 },
		{ "CTRL_expressions_mouthSharpCornerPullR", 0.5 },
		{ "CTRL_expressions_mouthStickyDC", 0.5 },
		{ "CTRL_expressions_mouthStickyDINL", 0.5 },
		{ "CTRL_expressions_mouthStickyDINR", 0.5 },
		{ "CTRL_expressions_mouthStickyDOUTL", 0.5 },
		{ "CTRL_expressions_mouthStickyDOUTR", 0.5 },
		{ "CTRL_expressions_mouthStickyUC", 0.5 },
		{ "CTRL_expressions_mouthStickyUINL", 0.5 },
		{ "CTRL_expressions_mouthStickyUINR", 0.5 },
		{ "CTRL_expressions_mouthStickyUOUTL", 0.5 },
		{ "CTRL_expressions_mouthStickyUOUTR", 0.5 },
		{ "CTRL_expressions_mouthStretchL", 0.5 },
		{ "CTRL_expressions_mouthStretchLipsCloseL", 0.5 },
		{ "CTRL_expressions_mouthStretchLipsCloseR", 0.5 },
		{ "CTRL_expressions_mouthStretchR", 0.5 },
		{ "CTRL_expressions_mouthUp", 0.5 },
		{ "CTRL_expressions_mouthUpperLipBiteL", 0.5 },
		{ "CTRL_expressions_mouthUpperLipBiteR", 0.5 },
		{ "CTRL_expressions_mouthUpperLipRaiseL", 0.5 },
		{ "CTRL_expressions_mouthUpperLipRaiseR", 0.5 },
		{ "CTRL_expressions_mouthUpperLipRollInL", 0.5 },
		{ "CTRL_expressions_mouthUpperLipRollInR", 0.5 },
		{ "CTRL_expressions_mouthUpperLipRollOutL", 0 },
		{ "CTRL_expressions_mouthUpperLipRollOutR", 0 },
		{ "CTRL_expressions_mouthUpperLipShiftLeft", 0.5 },
		{ "CTRL_expressions_mouthUpperLipShiftRight", 0 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethL", 0.5 },
		{ "CTRL_expressions_mouthUpperLipTowardsTeethR", 0.5 },
		{ "CTRL_expressions_neckDigastricDown", 0 },
		{ "CTRL_expressions_neckDigastricUp", 0.5 },
		{ "CTRL_expressions_neckMastoidContractL", 0.5 },
		{ "CTRL_expressions_neckMastoidContractR", 0.5 },
		{ "CTRL_expressions_neckStretchL", 0.5 },
		{ "CTRL_expressions_neckStretchR", 0.5 },
		{ "CTRL_expressions_neckSwallowPh1", 0 },
		{ "CTRL_expressions_neckSwallowPh2", 0.5 },
		{ "CTRL_expressions_neckSwallowPh3", 0.5 },
		{ "CTRL_expressions_neckSwallowPh4", 0 },
		{ "CTRL_expressions_neckThroatDown", 0 },
		{ "CTRL_expressions_neckThroatExhale", 0 },
		{ "CTRL_expressions_neckThroatInhale", 0.5 },
		{ "CTRL_expressions_neckThroatUp", 0.5 },
		{ "CTRL_expressions_noseNasolabialDeepenL", 0.5 },
		{ "CTRL_expressions_noseNasolabialDeepenR", 0.5 },
		{ "CTRL_expressions_noseNostrilCompressL", 0 },
		{ "CTRL_expressions_noseNostrilCompressR", 0 },
		{ "CTRL_expressions_noseNostrilDepressL", 0 },
		{ "CTRL_expressions_noseNostrilDepressR", 0 },
		{ "CTRL_expressions_noseNostrilDilateL", 0.5 },
		{ "CTRL_expressions_noseNostrilDilateR", 0.5 },
		{ "CTRL_expressions_noseWrinkleL", 0.5 },
		{ "CTRL_expressions_noseWrinkleR", 0.5 },
		{ "CTRL_expressions_noseWrinkleUpperL", 0.5 },
		{ "CTRL_expressions_noseWrinkleUpperR", 0.5 },
		{ "CTRL_expressions_teethBackD", 0.5 },
		{ "CTRL_expressions_teethBackU", 0.5 },
		{ "CTRL_expressions_teethDownD", 0 },
		{ "CTRL_expressions_teethDownU", 0 },
		{ "CTRL_expressions_teethFwdD", 0 },
		{ "CTRL_expressions_teethFwdU", 0 },
		{ "CTRL_expressions_teethLeftD", 0.5 },
		{ "CTRL_expressions_teethLeftU", 0.5 },
		{ "CTRL_expressions_teethRightD", 0 },
		{ "CTRL_expressions_teethRightU", 0 },
		{ "CTRL_expressions_teethUpD", 0.5 },
		{ "CTRL_expressions_teethUpU", 0.5 },
		{ "CTRL_expressions_tongueBendDown", 0 },
		{ "CTRL_expressions_tongueBendUp", 0.5 },
		{ "CTRL_expressions_tongueDown", 0 },
		{ "CTRL_expressions_tongueIn", 0.5 },
		{ "CTRL_expressions_tongueLeft", 0.5 },
		{ "CTRL_expressions_tongueNarrow", 0 },
		{ "CTRL_expressions_tongueOut", 0 },
		{ "CTRL_expressions_tonguePress", 0.5 },
		{ "CTRL_expressions_tongueRight", 0 },
		{ "CTRL_expressions_tongueRoll", 0.5 },
		{ "CTRL_expressions_tongueThick", 0.5 },
		{ "CTRL_expressions_tongueThin", 0 },
		{ "CTRL_expressions_tongueTipDown", 0 },
		{ "CTRL_expressions_tongueTipLeft", 0.5 },
		{ "CTRL_expressions_tongueTipRight", 0 },
		{ "CTRL_expressions_tongueTipUp", 0.5 },
		{ "CTRL_expressions_tongueTwistLeft", 0.5 },
		{ "CTRL_expressions_tongueTwistRight", 0 },
		{ "CTRL_expressions_tongueUp", 0.5 },
		{ "CTRL_expressions_tongueWide", 0.5 }
	};
}
#endif