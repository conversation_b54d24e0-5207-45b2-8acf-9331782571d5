// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: metahuman_service_api.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_metahuman_5fservice_5fapi_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_metahuman_5fservice_5fapi_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3018000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3018000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_metahuman_5fservice_5fapi_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_metahuman_5fservice_5fapi_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_metahuman_5fservice_5fapi_2eproto;
namespace metahuman_service_api {
class AutorigRequest;
struct AutorigRequestDefaultTypeInternal;
extern AutorigRequestDefaultTypeInternal _AutorigRequest_default_instance_;
class Eyes;
struct EyesDefaultTypeInternal;
extern EyesDefaultTypeInternal _Eyes_default_instance_;
class Head;
struct HeadDefaultTypeInternal;
extern HeadDefaultTypeInternal _Head_default_instance_;
class Mesh;
struct MeshDefaultTypeInternal;
extern MeshDefaultTypeInternal _Mesh_default_instance_;
class Parameters;
struct ParametersDefaultTypeInternal;
extern ParametersDefaultTypeInternal _Parameters_default_instance_;
class Quality;
struct QualityDefaultTypeInternal;
extern QualityDefaultTypeInternal _Quality_default_instance_;
class UEVersion;
struct UEVersionDefaultTypeInternal;
extern UEVersionDefaultTypeInternal _UEVersion_default_instance_;
class Vertex;
struct VertexDefaultTypeInternal;
extern VertexDefaultTypeInternal _Vertex_default_instance_;
}  // namespace metahuman_service_api
PROTOBUF_NAMESPACE_OPEN
template<> ::metahuman_service_api::AutorigRequest* Arena::CreateMaybeMessage<::metahuman_service_api::AutorigRequest>(Arena*);
template<> ::metahuman_service_api::Eyes* Arena::CreateMaybeMessage<::metahuman_service_api::Eyes>(Arena*);
template<> ::metahuman_service_api::Head* Arena::CreateMaybeMessage<::metahuman_service_api::Head>(Arena*);
template<> ::metahuman_service_api::Mesh* Arena::CreateMaybeMessage<::metahuman_service_api::Mesh>(Arena*);
template<> ::metahuman_service_api::Parameters* Arena::CreateMaybeMessage<::metahuman_service_api::Parameters>(Arena*);
template<> ::metahuman_service_api::Quality* Arena::CreateMaybeMessage<::metahuman_service_api::Quality>(Arena*);
template<> ::metahuman_service_api::UEVersion* Arena::CreateMaybeMessage<::metahuman_service_api::UEVersion>(Arena*);
template<> ::metahuman_service_api::Vertex* Arena::CreateMaybeMessage<::metahuman_service_api::Vertex>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace metahuman_service_api {

enum RigType : int {
  RIG_TYPE_UNKNOWN = 0,
  RIG_TYPE_JOINTS_ONLY = 1,
  RIG_TYPE_JOINTS_AND_BLENDSHAPES = 2,
  RigType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RigType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RigType_IsValid(int value);
constexpr RigType RigType_MIN = RIG_TYPE_UNKNOWN;
constexpr RigType RigType_MAX = RIG_TYPE_JOINTS_AND_BLENDSHAPES;
constexpr int RigType_ARRAYSIZE = RigType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RigType_descriptor();
template<typename T>
inline const std::string& RigType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RigType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RigType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RigType_descriptor(), enum_t_value);
}
inline bool RigType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RigType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RigType>(
    RigType_descriptor(), name, value);
}
enum RefinementLevel : int {
  REFINEMENT_LEVEL_UNKNOWN = 0,
  REFINEMENT_LEVEL_NONE = 1,
  REFINEMENT_LEVEL_MEDIUM = 2,
  RefinementLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  RefinementLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool RefinementLevel_IsValid(int value);
constexpr RefinementLevel RefinementLevel_MIN = REFINEMENT_LEVEL_UNKNOWN;
constexpr RefinementLevel RefinementLevel_MAX = REFINEMENT_LEVEL_MEDIUM;
constexpr int RefinementLevel_ARRAYSIZE = RefinementLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RefinementLevel_descriptor();
template<typename T>
inline const std::string& RefinementLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RefinementLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RefinementLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RefinementLevel_descriptor(), enum_t_value);
}
inline bool RefinementLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RefinementLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RefinementLevel>(
    RefinementLevel_descriptor(), name, value);
}
enum ExportLayers : int {
  EXPORT_LAYERS_UNKNOWN = 0,
  EXPORT_LAYERS_NONE = 1,
  EXPORT_LAYERS_RBF = 2,
  ExportLayers_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  ExportLayers_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool ExportLayers_IsValid(int value);
constexpr ExportLayers ExportLayers_MIN = EXPORT_LAYERS_UNKNOWN;
constexpr ExportLayers ExportLayers_MAX = EXPORT_LAYERS_RBF;
constexpr int ExportLayers_ARRAYSIZE = ExportLayers_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ExportLayers_descriptor();
template<typename T>
inline const std::string& ExportLayers_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ExportLayers>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ExportLayers_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ExportLayers_descriptor(), enum_t_value);
}
inline bool ExportLayers_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ExportLayers* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ExportLayers>(
    ExportLayers_descriptor(), name, value);
}
// ===================================================================

class Vertex final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Vertex) */ {
 public:
  inline Vertex() : Vertex(nullptr) {}
  ~Vertex() override;
  explicit constexpr Vertex(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vertex(const Vertex& from);
  Vertex(Vertex&& from) noexcept
    : Vertex() {
    *this = ::std::move(from);
  }

  inline Vertex& operator=(const Vertex& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vertex& operator=(Vertex&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vertex& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vertex* internal_default_instance() {
    return reinterpret_cast<const Vertex*>(
               &_Vertex_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Vertex& a, Vertex& b) {
    a.Swap(&b);
  }
  inline void Swap(Vertex* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vertex* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Vertex* New() const final {
    return new Vertex();
  }

  Vertex* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Vertex>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vertex& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Vertex& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vertex* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Vertex";
  }
  protected:
  explicit Vertex(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // float z = 3;
  void clear_z();
  float z() const;
  void set_z(float value);
  private:
  float _internal_z() const;
  void _internal_set_z(float value);
  public:

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Vertex)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float x_;
  float y_;
  float z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class Mesh final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Mesh) */ {
 public:
  inline Mesh() : Mesh(nullptr) {}
  ~Mesh() override;
  explicit constexpr Mesh(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Mesh(const Mesh& from);
  Mesh(Mesh&& from) noexcept
    : Mesh() {
    *this = ::std::move(from);
  }

  inline Mesh& operator=(const Mesh& from) {
    CopyFrom(from);
    return *this;
  }
  inline Mesh& operator=(Mesh&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Mesh& default_instance() {
    return *internal_default_instance();
  }
  static inline const Mesh* internal_default_instance() {
    return reinterpret_cast<const Mesh*>(
               &_Mesh_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Mesh& a, Mesh& b) {
    a.Swap(&b);
  }
  inline void Swap(Mesh* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Mesh* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Mesh* New() const final {
    return new Mesh();
  }

  Mesh* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Mesh>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Mesh& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Mesh& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Mesh* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Mesh";
  }
  protected:
  explicit Mesh(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVerticesFieldNumber = 1,
  };
  // repeated .metahuman_service_api.Vertex vertices = 1;
  int vertices_size() const;
  private:
  int _internal_vertices_size() const;
  public:
  void clear_vertices();
  ::metahuman_service_api::Vertex* mutable_vertices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::metahuman_service_api::Vertex >*
      mutable_vertices();
  private:
  const ::metahuman_service_api::Vertex& _internal_vertices(int index) const;
  ::metahuman_service_api::Vertex* _internal_add_vertices();
  public:
  const ::metahuman_service_api::Vertex& vertices(int index) const;
  ::metahuman_service_api::Vertex* add_vertices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::metahuman_service_api::Vertex >&
      vertices() const;

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Mesh)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::metahuman_service_api::Vertex > vertices_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class Eyes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Eyes) */ {
 public:
  inline Eyes() : Eyes(nullptr) {}
  ~Eyes() override;
  explicit constexpr Eyes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Eyes(const Eyes& from);
  Eyes(Eyes&& from) noexcept
    : Eyes() {
    *this = ::std::move(from);
  }

  inline Eyes& operator=(const Eyes& from) {
    CopyFrom(from);
    return *this;
  }
  inline Eyes& operator=(Eyes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Eyes& default_instance() {
    return *internal_default_instance();
  }
  static inline const Eyes* internal_default_instance() {
    return reinterpret_cast<const Eyes*>(
               &_Eyes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Eyes& a, Eyes& b) {
    a.Swap(&b);
  }
  inline void Swap(Eyes* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Eyes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Eyes* New() const final {
    return new Eyes();
  }

  Eyes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Eyes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Eyes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Eyes& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Eyes* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Eyes";
  }
  protected:
  explicit Eyes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLeftFieldNumber = 1,
    kRightFieldNumber = 2,
    kEdgeFieldNumber = 3,
    kShellFieldNumber = 4,
    kLashesFieldNumber = 5,
  };
  // .metahuman_service_api.Mesh left = 1;
  bool has_left() const;
  private:
  bool _internal_has_left() const;
  public:
  void clear_left();
  const ::metahuman_service_api::Mesh& left() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_left();
  ::metahuman_service_api::Mesh* mutable_left();
  void set_allocated_left(::metahuman_service_api::Mesh* left);
  private:
  const ::metahuman_service_api::Mesh& _internal_left() const;
  ::metahuman_service_api::Mesh* _internal_mutable_left();
  public:
  void unsafe_arena_set_allocated_left(
      ::metahuman_service_api::Mesh* left);
  ::metahuman_service_api::Mesh* unsafe_arena_release_left();

  // .metahuman_service_api.Mesh right = 2;
  bool has_right() const;
  private:
  bool _internal_has_right() const;
  public:
  void clear_right();
  const ::metahuman_service_api::Mesh& right() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_right();
  ::metahuman_service_api::Mesh* mutable_right();
  void set_allocated_right(::metahuman_service_api::Mesh* right);
  private:
  const ::metahuman_service_api::Mesh& _internal_right() const;
  ::metahuman_service_api::Mesh* _internal_mutable_right();
  public:
  void unsafe_arena_set_allocated_right(
      ::metahuman_service_api::Mesh* right);
  ::metahuman_service_api::Mesh* unsafe_arena_release_right();

  // .metahuman_service_api.Mesh edge = 3;
  bool has_edge() const;
  private:
  bool _internal_has_edge() const;
  public:
  void clear_edge();
  const ::metahuman_service_api::Mesh& edge() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_edge();
  ::metahuman_service_api::Mesh* mutable_edge();
  void set_allocated_edge(::metahuman_service_api::Mesh* edge);
  private:
  const ::metahuman_service_api::Mesh& _internal_edge() const;
  ::metahuman_service_api::Mesh* _internal_mutable_edge();
  public:
  void unsafe_arena_set_allocated_edge(
      ::metahuman_service_api::Mesh* edge);
  ::metahuman_service_api::Mesh* unsafe_arena_release_edge();

  // .metahuman_service_api.Mesh shell = 4;
  bool has_shell() const;
  private:
  bool _internal_has_shell() const;
  public:
  void clear_shell();
  const ::metahuman_service_api::Mesh& shell() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_shell();
  ::metahuman_service_api::Mesh* mutable_shell();
  void set_allocated_shell(::metahuman_service_api::Mesh* shell);
  private:
  const ::metahuman_service_api::Mesh& _internal_shell() const;
  ::metahuman_service_api::Mesh* _internal_mutable_shell();
  public:
  void unsafe_arena_set_allocated_shell(
      ::metahuman_service_api::Mesh* shell);
  ::metahuman_service_api::Mesh* unsafe_arena_release_shell();

  // .metahuman_service_api.Mesh lashes = 5;
  bool has_lashes() const;
  private:
  bool _internal_has_lashes() const;
  public:
  void clear_lashes();
  const ::metahuman_service_api::Mesh& lashes() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_lashes();
  ::metahuman_service_api::Mesh* mutable_lashes();
  void set_allocated_lashes(::metahuman_service_api::Mesh* lashes);
  private:
  const ::metahuman_service_api::Mesh& _internal_lashes() const;
  ::metahuman_service_api::Mesh* _internal_mutable_lashes();
  public:
  void unsafe_arena_set_allocated_lashes(
      ::metahuman_service_api::Mesh* lashes);
  ::metahuman_service_api::Mesh* unsafe_arena_release_lashes();

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Eyes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::metahuman_service_api::Mesh* left_;
  ::metahuman_service_api::Mesh* right_;
  ::metahuman_service_api::Mesh* edge_;
  ::metahuman_service_api::Mesh* shell_;
  ::metahuman_service_api::Mesh* lashes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class Head final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Head) */ {
 public:
  inline Head() : Head(nullptr) {}
  ~Head() override;
  explicit constexpr Head(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Head(const Head& from);
  Head(Head&& from) noexcept
    : Head() {
    *this = ::std::move(from);
  }

  inline Head& operator=(const Head& from) {
    CopyFrom(from);
    return *this;
  }
  inline Head& operator=(Head&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Head& default_instance() {
    return *internal_default_instance();
  }
  static inline const Head* internal_default_instance() {
    return reinterpret_cast<const Head*>(
               &_Head_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Head& a, Head& b) {
    a.Swap(&b);
  }
  inline void Swap(Head* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Head* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Head* New() const final {
    return new Head();
  }

  Head* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Head>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Head& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Head& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Head* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Head";
  }
  protected:
  explicit Head(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFaceFieldNumber = 1,
    kEyesFieldNumber = 2,
    kTeethFieldNumber = 3,
    kCartilageFieldNumber = 4,
    kSalivaFieldNumber = 5,
  };
  // .metahuman_service_api.Mesh face = 1;
  bool has_face() const;
  private:
  bool _internal_has_face() const;
  public:
  void clear_face();
  const ::metahuman_service_api::Mesh& face() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_face();
  ::metahuman_service_api::Mesh* mutable_face();
  void set_allocated_face(::metahuman_service_api::Mesh* face);
  private:
  const ::metahuman_service_api::Mesh& _internal_face() const;
  ::metahuman_service_api::Mesh* _internal_mutable_face();
  public:
  void unsafe_arena_set_allocated_face(
      ::metahuman_service_api::Mesh* face);
  ::metahuman_service_api::Mesh* unsafe_arena_release_face();

  // .metahuman_service_api.Eyes eyes = 2;
  bool has_eyes() const;
  private:
  bool _internal_has_eyes() const;
  public:
  void clear_eyes();
  const ::metahuman_service_api::Eyes& eyes() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Eyes* release_eyes();
  ::metahuman_service_api::Eyes* mutable_eyes();
  void set_allocated_eyes(::metahuman_service_api::Eyes* eyes);
  private:
  const ::metahuman_service_api::Eyes& _internal_eyes() const;
  ::metahuman_service_api::Eyes* _internal_mutable_eyes();
  public:
  void unsafe_arena_set_allocated_eyes(
      ::metahuman_service_api::Eyes* eyes);
  ::metahuman_service_api::Eyes* unsafe_arena_release_eyes();

  // .metahuman_service_api.Mesh teeth = 3;
  bool has_teeth() const;
  private:
  bool _internal_has_teeth() const;
  public:
  void clear_teeth();
  const ::metahuman_service_api::Mesh& teeth() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_teeth();
  ::metahuman_service_api::Mesh* mutable_teeth();
  void set_allocated_teeth(::metahuman_service_api::Mesh* teeth);
  private:
  const ::metahuman_service_api::Mesh& _internal_teeth() const;
  ::metahuman_service_api::Mesh* _internal_mutable_teeth();
  public:
  void unsafe_arena_set_allocated_teeth(
      ::metahuman_service_api::Mesh* teeth);
  ::metahuman_service_api::Mesh* unsafe_arena_release_teeth();

  // .metahuman_service_api.Mesh cartilage = 4;
  bool has_cartilage() const;
  private:
  bool _internal_has_cartilage() const;
  public:
  void clear_cartilage();
  const ::metahuman_service_api::Mesh& cartilage() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_cartilage();
  ::metahuman_service_api::Mesh* mutable_cartilage();
  void set_allocated_cartilage(::metahuman_service_api::Mesh* cartilage);
  private:
  const ::metahuman_service_api::Mesh& _internal_cartilage() const;
  ::metahuman_service_api::Mesh* _internal_mutable_cartilage();
  public:
  void unsafe_arena_set_allocated_cartilage(
      ::metahuman_service_api::Mesh* cartilage);
  ::metahuman_service_api::Mesh* unsafe_arena_release_cartilage();

  // .metahuman_service_api.Mesh saliva = 5;
  bool has_saliva() const;
  private:
  bool _internal_has_saliva() const;
  public:
  void clear_saliva();
  const ::metahuman_service_api::Mesh& saliva() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Mesh* release_saliva();
  ::metahuman_service_api::Mesh* mutable_saliva();
  void set_allocated_saliva(::metahuman_service_api::Mesh* saliva);
  private:
  const ::metahuman_service_api::Mesh& _internal_saliva() const;
  ::metahuman_service_api::Mesh* _internal_mutable_saliva();
  public:
  void unsafe_arena_set_allocated_saliva(
      ::metahuman_service_api::Mesh* saliva);
  ::metahuman_service_api::Mesh* unsafe_arena_release_saliva();

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Head)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::metahuman_service_api::Mesh* face_;
  ::metahuman_service_api::Eyes* eyes_;
  ::metahuman_service_api::Mesh* teeth_;
  ::metahuman_service_api::Mesh* cartilage_;
  ::metahuman_service_api::Mesh* saliva_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class Parameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Parameters) */ {
 public:
  inline Parameters() : Parameters(nullptr) {}
  ~Parameters() override;
  explicit constexpr Parameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Parameters(const Parameters& from);
  Parameters(Parameters&& from) noexcept
    : Parameters() {
    *this = ::std::move(from);
  }

  inline Parameters& operator=(const Parameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline Parameters& operator=(Parameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Parameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const Parameters* internal_default_instance() {
    return reinterpret_cast<const Parameters*>(
               &_Parameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Parameters& a, Parameters& b) {
    a.Swap(&b);
  }
  inline void Swap(Parameters* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Parameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Parameters* New() const final {
    return new Parameters();
  }

  Parameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Parameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Parameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Parameters& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Parameters* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Parameters";
  }
  protected:
  explicit Parameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBindPoseFieldNumber = 1,
    kSolverCoefficientsFieldNumber = 2,
    kModelIdFieldNumber = 3,
  };
  // repeated float bind_pose = 1;
  int bind_pose_size() const;
  private:
  int _internal_bind_pose_size() const;
  public:
  void clear_bind_pose();
  private:
  float _internal_bind_pose(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_bind_pose() const;
  void _internal_add_bind_pose(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_bind_pose();
  public:
  float bind_pose(int index) const;
  void set_bind_pose(int index, float value);
  void add_bind_pose(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      bind_pose() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_bind_pose();

  // repeated float solver_coefficients = 2;
  int solver_coefficients_size() const;
  private:
  int _internal_solver_coefficients_size() const;
  public:
  void clear_solver_coefficients();
  private:
  float _internal_solver_coefficients(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_solver_coefficients() const;
  void _internal_add_solver_coefficients(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_solver_coefficients();
  public:
  float solver_coefficients(int index) const;
  void set_solver_coefficients(int index, float value);
  void add_solver_coefficients(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      solver_coefficients() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_solver_coefficients();

  // string model_id = 3;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_MUST_USE_RESULT std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Parameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > bind_pose_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > solver_coefficients_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class Quality final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.Quality) */ {
 public:
  inline Quality() : Quality(nullptr) {}
  ~Quality() override;
  explicit constexpr Quality(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Quality(const Quality& from);
  Quality(Quality&& from) noexcept
    : Quality() {
    *this = ::std::move(from);
  }

  inline Quality& operator=(const Quality& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quality& operator=(Quality&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Quality& default_instance() {
    return *internal_default_instance();
  }
  static inline const Quality* internal_default_instance() {
    return reinterpret_cast<const Quality*>(
               &_Quality_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Quality& a, Quality& b) {
    a.Swap(&b);
  }
  inline void Swap(Quality* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quality* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Quality* New() const final {
    return new Quality();
  }

  Quality* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Quality>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Quality& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Quality& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quality* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.Quality";
  }
  protected:
  explicit Quality(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRigTypeFieldNumber = 1,
    kRefinementLevelFieldNumber = 2,
    kExportLayersFieldNumber = 3,
  };
  // .metahuman_service_api.RigType rig_type = 1;
  void clear_rig_type();
  ::metahuman_service_api::RigType rig_type() const;
  void set_rig_type(::metahuman_service_api::RigType value);
  private:
  ::metahuman_service_api::RigType _internal_rig_type() const;
  void _internal_set_rig_type(::metahuman_service_api::RigType value);
  public:

  // .metahuman_service_api.RefinementLevel refinement_level = 2;
  void clear_refinement_level();
  ::metahuman_service_api::RefinementLevel refinement_level() const;
  void set_refinement_level(::metahuman_service_api::RefinementLevel value);
  private:
  ::metahuman_service_api::RefinementLevel _internal_refinement_level() const;
  void _internal_set_refinement_level(::metahuman_service_api::RefinementLevel value);
  public:

  // .metahuman_service_api.ExportLayers export_layers = 3;
  void clear_export_layers();
  ::metahuman_service_api::ExportLayers export_layers() const;
  void set_export_layers(::metahuman_service_api::ExportLayers value);
  private:
  ::metahuman_service_api::ExportLayers _internal_export_layers() const;
  void _internal_set_export_layers(::metahuman_service_api::ExportLayers value);
  public:

  // @@protoc_insertion_point(class_scope:metahuman_service_api.Quality)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int rig_type_;
  int refinement_level_;
  int export_layers_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class UEVersion final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.UEVersion) */ {
 public:
  inline UEVersion() : UEVersion(nullptr) {}
  ~UEVersion() override;
  explicit constexpr UEVersion(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UEVersion(const UEVersion& from);
  UEVersion(UEVersion&& from) noexcept
    : UEVersion() {
    *this = ::std::move(from);
  }

  inline UEVersion& operator=(const UEVersion& from) {
    CopyFrom(from);
    return *this;
  }
  inline UEVersion& operator=(UEVersion&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UEVersion& default_instance() {
    return *internal_default_instance();
  }
  static inline const UEVersion* internal_default_instance() {
    return reinterpret_cast<const UEVersion*>(
               &_UEVersion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(UEVersion& a, UEVersion& b) {
    a.Swap(&b);
  }
  inline void Swap(UEVersion* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UEVersion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UEVersion* New() const final {
    return new UEVersion();
  }

  UEVersion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UEVersion>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UEVersion& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UEVersion& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UEVersion* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.UEVersion";
  }
  protected:
  explicit UEVersion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
  };
  // uint32 major = 1;
  void clear_major();
  ::PROTOBUF_NAMESPACE_ID::uint32 major() const;
  void set_major(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_major() const;
  void _internal_set_major(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 minor = 2;
  void clear_minor();
  ::PROTOBUF_NAMESPACE_ID::uint32 minor() const;
  void set_minor(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_minor() const;
  void _internal_set_minor(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:metahuman_service_api.UEVersion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::uint32 major_;
  ::PROTOBUF_NAMESPACE_ID::uint32 minor_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// -------------------------------------------------------------------

class AutorigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metahuman_service_api.AutorigRequest) */ {
 public:
  inline AutorigRequest() : AutorigRequest(nullptr) {}
  ~AutorigRequest() override;
  explicit constexpr AutorigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutorigRequest(const AutorigRequest& from);
  AutorigRequest(AutorigRequest&& from) noexcept
    : AutorigRequest() {
    *this = ::std::move(from);
  }

  inline AutorigRequest& operator=(const AutorigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutorigRequest& operator=(AutorigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutorigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutorigRequest* internal_default_instance() {
    return reinterpret_cast<const AutorigRequest*>(
               &_AutorigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AutorigRequest& a, AutorigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AutorigRequest* other) {
    if (other == this) return;
    if (GetOwningArena() == other->GetOwningArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutorigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline AutorigRequest* New() const final {
    return new AutorigRequest();
  }

  AutorigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<AutorigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutorigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AutorigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutorigRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metahuman_service_api.AutorigRequest";
  }
  protected:
  explicit AutorigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeadFieldNumber = 1,
    kParametersFieldNumber = 4,
    kQualityFieldNumber = 5,
    kUeVersionFieldNumber = 6,
    kHighFrequencyIndexFieldNumber = 2,
    kToTargetScaleFieldNumber = 3,
  };
  // .metahuman_service_api.Head head = 1;
  bool has_head() const;
  private:
  bool _internal_has_head() const;
  public:
  void clear_head();
  const ::metahuman_service_api::Head& head() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Head* release_head();
  ::metahuman_service_api::Head* mutable_head();
  void set_allocated_head(::metahuman_service_api::Head* head);
  private:
  const ::metahuman_service_api::Head& _internal_head() const;
  ::metahuman_service_api::Head* _internal_mutable_head();
  public:
  void unsafe_arena_set_allocated_head(
      ::metahuman_service_api::Head* head);
  ::metahuman_service_api::Head* unsafe_arena_release_head();

  // optional .metahuman_service_api.Parameters parameters = 4;
  bool has_parameters() const;
  private:
  bool _internal_has_parameters() const;
  public:
  void clear_parameters();
  const ::metahuman_service_api::Parameters& parameters() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Parameters* release_parameters();
  ::metahuman_service_api::Parameters* mutable_parameters();
  void set_allocated_parameters(::metahuman_service_api::Parameters* parameters);
  private:
  const ::metahuman_service_api::Parameters& _internal_parameters() const;
  ::metahuman_service_api::Parameters* _internal_mutable_parameters();
  public:
  void unsafe_arena_set_allocated_parameters(
      ::metahuman_service_api::Parameters* parameters);
  ::metahuman_service_api::Parameters* unsafe_arena_release_parameters();

  // .metahuman_service_api.Quality quality = 5;
  bool has_quality() const;
  private:
  bool _internal_has_quality() const;
  public:
  void clear_quality();
  const ::metahuman_service_api::Quality& quality() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::Quality* release_quality();
  ::metahuman_service_api::Quality* mutable_quality();
  void set_allocated_quality(::metahuman_service_api::Quality* quality);
  private:
  const ::metahuman_service_api::Quality& _internal_quality() const;
  ::metahuman_service_api::Quality* _internal_mutable_quality();
  public:
  void unsafe_arena_set_allocated_quality(
      ::metahuman_service_api::Quality* quality);
  ::metahuman_service_api::Quality* unsafe_arena_release_quality();

  // .metahuman_service_api.UEVersion ue_version = 6;
  bool has_ue_version() const;
  private:
  bool _internal_has_ue_version() const;
  public:
  void clear_ue_version();
  const ::metahuman_service_api::UEVersion& ue_version() const;
  PROTOBUF_MUST_USE_RESULT ::metahuman_service_api::UEVersion* release_ue_version();
  ::metahuman_service_api::UEVersion* mutable_ue_version();
  void set_allocated_ue_version(::metahuman_service_api::UEVersion* ue_version);
  private:
  const ::metahuman_service_api::UEVersion& _internal_ue_version() const;
  ::metahuman_service_api::UEVersion* _internal_mutable_ue_version();
  public:
  void unsafe_arena_set_allocated_ue_version(
      ::metahuman_service_api::UEVersion* ue_version);
  ::metahuman_service_api::UEVersion* unsafe_arena_release_ue_version();

  // int32 high_frequency_index = 2;
  void clear_high_frequency_index();
  ::PROTOBUF_NAMESPACE_ID::int32 high_frequency_index() const;
  void set_high_frequency_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_high_frequency_index() const;
  void _internal_set_high_frequency_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // float to_target_scale = 3;
  void clear_to_target_scale();
  float to_target_scale() const;
  void set_to_target_scale(float value);
  private:
  float _internal_to_target_scale() const;
  void _internal_set_to_target_scale(float value);
  public:

  // @@protoc_insertion_point(class_scope:metahuman_service_api.AutorigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::metahuman_service_api::Head* head_;
  ::metahuman_service_api::Parameters* parameters_;
  ::metahuman_service_api::Quality* quality_;
  ::metahuman_service_api::UEVersion* ue_version_;
  ::PROTOBUF_NAMESPACE_ID::int32 high_frequency_index_;
  float to_target_scale_;
  friend struct ::TableStruct_metahuman_5fservice_5fapi_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Vertex

// float x = 1;
inline void Vertex::clear_x() {
  x_ = 0;
}
inline float Vertex::_internal_x() const {
  return x_;
}
inline float Vertex::x() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Vertex.x)
  return _internal_x();
}
inline void Vertex::_internal_set_x(float value) {
  
  x_ = value;
}
inline void Vertex::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Vertex.x)
}

// float y = 2;
inline void Vertex::clear_y() {
  y_ = 0;
}
inline float Vertex::_internal_y() const {
  return y_;
}
inline float Vertex::y() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Vertex.y)
  return _internal_y();
}
inline void Vertex::_internal_set_y(float value) {
  
  y_ = value;
}
inline void Vertex::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Vertex.y)
}

// float z = 3;
inline void Vertex::clear_z() {
  z_ = 0;
}
inline float Vertex::_internal_z() const {
  return z_;
}
inline float Vertex::z() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Vertex.z)
  return _internal_z();
}
inline void Vertex::_internal_set_z(float value) {
  
  z_ = value;
}
inline void Vertex::set_z(float value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Vertex.z)
}

// -------------------------------------------------------------------

// Mesh

// repeated .metahuman_service_api.Vertex vertices = 1;
inline int Mesh::_internal_vertices_size() const {
  return vertices_.size();
}
inline int Mesh::vertices_size() const {
  return _internal_vertices_size();
}
inline void Mesh::clear_vertices() {
  vertices_.Clear();
}
inline ::metahuman_service_api::Vertex* Mesh::mutable_vertices(int index) {
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Mesh.vertices)
  return vertices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::metahuman_service_api::Vertex >*
Mesh::mutable_vertices() {
  // @@protoc_insertion_point(field_mutable_list:metahuman_service_api.Mesh.vertices)
  return &vertices_;
}
inline const ::metahuman_service_api::Vertex& Mesh::_internal_vertices(int index) const {
  return vertices_.Get(index);
}
inline const ::metahuman_service_api::Vertex& Mesh::vertices(int index) const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Mesh.vertices)
  return _internal_vertices(index);
}
inline ::metahuman_service_api::Vertex* Mesh::_internal_add_vertices() {
  return vertices_.Add();
}
inline ::metahuman_service_api::Vertex* Mesh::add_vertices() {
  ::metahuman_service_api::Vertex* _add = _internal_add_vertices();
  // @@protoc_insertion_point(field_add:metahuman_service_api.Mesh.vertices)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::metahuman_service_api::Vertex >&
Mesh::vertices() const {
  // @@protoc_insertion_point(field_list:metahuman_service_api.Mesh.vertices)
  return vertices_;
}

// -------------------------------------------------------------------

// Eyes

// .metahuman_service_api.Mesh left = 1;
inline bool Eyes::_internal_has_left() const {
  return this != internal_default_instance() && left_ != nullptr;
}
inline bool Eyes::has_left() const {
  return _internal_has_left();
}
inline void Eyes::clear_left() {
  if (GetArenaForAllocation() == nullptr && left_ != nullptr) {
    delete left_;
  }
  left_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Eyes::_internal_left() const {
  const ::metahuman_service_api::Mesh* p = left_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Eyes::left() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Eyes.left)
  return _internal_left();
}
inline void Eyes::unsafe_arena_set_allocated_left(
    ::metahuman_service_api::Mesh* left) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(left_);
  }
  left_ = left;
  if (left) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Eyes.left)
}
inline ::metahuman_service_api::Mesh* Eyes::release_left() {
  
  ::metahuman_service_api::Mesh* temp = left_;
  left_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::unsafe_arena_release_left() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Eyes.left)
  
  ::metahuman_service_api::Mesh* temp = left_;
  left_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::_internal_mutable_left() {
  
  if (left_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    left_ = p;
  }
  return left_;
}
inline ::metahuman_service_api::Mesh* Eyes::mutable_left() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_left();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Eyes.left)
  return _msg;
}
inline void Eyes::set_allocated_left(::metahuman_service_api::Mesh* left) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete left_;
  }
  if (left) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(left);
    if (message_arena != submessage_arena) {
      left = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, left, submessage_arena);
    }
    
  } else {
    
  }
  left_ = left;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Eyes.left)
}

// .metahuman_service_api.Mesh right = 2;
inline bool Eyes::_internal_has_right() const {
  return this != internal_default_instance() && right_ != nullptr;
}
inline bool Eyes::has_right() const {
  return _internal_has_right();
}
inline void Eyes::clear_right() {
  if (GetArenaForAllocation() == nullptr && right_ != nullptr) {
    delete right_;
  }
  right_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Eyes::_internal_right() const {
  const ::metahuman_service_api::Mesh* p = right_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Eyes::right() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Eyes.right)
  return _internal_right();
}
inline void Eyes::unsafe_arena_set_allocated_right(
    ::metahuman_service_api::Mesh* right) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(right_);
  }
  right_ = right;
  if (right) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Eyes.right)
}
inline ::metahuman_service_api::Mesh* Eyes::release_right() {
  
  ::metahuman_service_api::Mesh* temp = right_;
  right_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::unsafe_arena_release_right() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Eyes.right)
  
  ::metahuman_service_api::Mesh* temp = right_;
  right_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::_internal_mutable_right() {
  
  if (right_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    right_ = p;
  }
  return right_;
}
inline ::metahuman_service_api::Mesh* Eyes::mutable_right() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_right();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Eyes.right)
  return _msg;
}
inline void Eyes::set_allocated_right(::metahuman_service_api::Mesh* right) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete right_;
  }
  if (right) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(right);
    if (message_arena != submessage_arena) {
      right = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, right, submessage_arena);
    }
    
  } else {
    
  }
  right_ = right;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Eyes.right)
}

// .metahuman_service_api.Mesh edge = 3;
inline bool Eyes::_internal_has_edge() const {
  return this != internal_default_instance() && edge_ != nullptr;
}
inline bool Eyes::has_edge() const {
  return _internal_has_edge();
}
inline void Eyes::clear_edge() {
  if (GetArenaForAllocation() == nullptr && edge_ != nullptr) {
    delete edge_;
  }
  edge_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Eyes::_internal_edge() const {
  const ::metahuman_service_api::Mesh* p = edge_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Eyes::edge() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Eyes.edge)
  return _internal_edge();
}
inline void Eyes::unsafe_arena_set_allocated_edge(
    ::metahuman_service_api::Mesh* edge) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(edge_);
  }
  edge_ = edge;
  if (edge) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Eyes.edge)
}
inline ::metahuman_service_api::Mesh* Eyes::release_edge() {
  
  ::metahuman_service_api::Mesh* temp = edge_;
  edge_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::unsafe_arena_release_edge() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Eyes.edge)
  
  ::metahuman_service_api::Mesh* temp = edge_;
  edge_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::_internal_mutable_edge() {
  
  if (edge_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    edge_ = p;
  }
  return edge_;
}
inline ::metahuman_service_api::Mesh* Eyes::mutable_edge() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_edge();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Eyes.edge)
  return _msg;
}
inline void Eyes::set_allocated_edge(::metahuman_service_api::Mesh* edge) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete edge_;
  }
  if (edge) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(edge);
    if (message_arena != submessage_arena) {
      edge = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, edge, submessage_arena);
    }
    
  } else {
    
  }
  edge_ = edge;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Eyes.edge)
}

// .metahuman_service_api.Mesh shell = 4;
inline bool Eyes::_internal_has_shell() const {
  return this != internal_default_instance() && shell_ != nullptr;
}
inline bool Eyes::has_shell() const {
  return _internal_has_shell();
}
inline void Eyes::clear_shell() {
  if (GetArenaForAllocation() == nullptr && shell_ != nullptr) {
    delete shell_;
  }
  shell_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Eyes::_internal_shell() const {
  const ::metahuman_service_api::Mesh* p = shell_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Eyes::shell() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Eyes.shell)
  return _internal_shell();
}
inline void Eyes::unsafe_arena_set_allocated_shell(
    ::metahuman_service_api::Mesh* shell) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shell_);
  }
  shell_ = shell;
  if (shell) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Eyes.shell)
}
inline ::metahuman_service_api::Mesh* Eyes::release_shell() {
  
  ::metahuman_service_api::Mesh* temp = shell_;
  shell_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::unsafe_arena_release_shell() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Eyes.shell)
  
  ::metahuman_service_api::Mesh* temp = shell_;
  shell_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::_internal_mutable_shell() {
  
  if (shell_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    shell_ = p;
  }
  return shell_;
}
inline ::metahuman_service_api::Mesh* Eyes::mutable_shell() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_shell();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Eyes.shell)
  return _msg;
}
inline void Eyes::set_allocated_shell(::metahuman_service_api::Mesh* shell) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete shell_;
  }
  if (shell) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(shell);
    if (message_arena != submessage_arena) {
      shell = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shell, submessage_arena);
    }
    
  } else {
    
  }
  shell_ = shell;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Eyes.shell)
}

// .metahuman_service_api.Mesh lashes = 5;
inline bool Eyes::_internal_has_lashes() const {
  return this != internal_default_instance() && lashes_ != nullptr;
}
inline bool Eyes::has_lashes() const {
  return _internal_has_lashes();
}
inline void Eyes::clear_lashes() {
  if (GetArenaForAllocation() == nullptr && lashes_ != nullptr) {
    delete lashes_;
  }
  lashes_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Eyes::_internal_lashes() const {
  const ::metahuman_service_api::Mesh* p = lashes_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Eyes::lashes() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Eyes.lashes)
  return _internal_lashes();
}
inline void Eyes::unsafe_arena_set_allocated_lashes(
    ::metahuman_service_api::Mesh* lashes) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lashes_);
  }
  lashes_ = lashes;
  if (lashes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Eyes.lashes)
}
inline ::metahuman_service_api::Mesh* Eyes::release_lashes() {
  
  ::metahuman_service_api::Mesh* temp = lashes_;
  lashes_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::unsafe_arena_release_lashes() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Eyes.lashes)
  
  ::metahuman_service_api::Mesh* temp = lashes_;
  lashes_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Eyes::_internal_mutable_lashes() {
  
  if (lashes_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    lashes_ = p;
  }
  return lashes_;
}
inline ::metahuman_service_api::Mesh* Eyes::mutable_lashes() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_lashes();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Eyes.lashes)
  return _msg;
}
inline void Eyes::set_allocated_lashes(::metahuman_service_api::Mesh* lashes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete lashes_;
  }
  if (lashes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(lashes);
    if (message_arena != submessage_arena) {
      lashes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lashes, submessage_arena);
    }
    
  } else {
    
  }
  lashes_ = lashes;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Eyes.lashes)
}

// -------------------------------------------------------------------

// Head

// .metahuman_service_api.Mesh face = 1;
inline bool Head::_internal_has_face() const {
  return this != internal_default_instance() && face_ != nullptr;
}
inline bool Head::has_face() const {
  return _internal_has_face();
}
inline void Head::clear_face() {
  if (GetArenaForAllocation() == nullptr && face_ != nullptr) {
    delete face_;
  }
  face_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Head::_internal_face() const {
  const ::metahuman_service_api::Mesh* p = face_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Head::face() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Head.face)
  return _internal_face();
}
inline void Head::unsafe_arena_set_allocated_face(
    ::metahuman_service_api::Mesh* face) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(face_);
  }
  face_ = face;
  if (face) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Head.face)
}
inline ::metahuman_service_api::Mesh* Head::release_face() {
  
  ::metahuman_service_api::Mesh* temp = face_;
  face_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::unsafe_arena_release_face() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Head.face)
  
  ::metahuman_service_api::Mesh* temp = face_;
  face_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::_internal_mutable_face() {
  
  if (face_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    face_ = p;
  }
  return face_;
}
inline ::metahuman_service_api::Mesh* Head::mutable_face() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_face();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Head.face)
  return _msg;
}
inline void Head::set_allocated_face(::metahuman_service_api::Mesh* face) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete face_;
  }
  if (face) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(face);
    if (message_arena != submessage_arena) {
      face = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, face, submessage_arena);
    }
    
  } else {
    
  }
  face_ = face;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Head.face)
}

// .metahuman_service_api.Eyes eyes = 2;
inline bool Head::_internal_has_eyes() const {
  return this != internal_default_instance() && eyes_ != nullptr;
}
inline bool Head::has_eyes() const {
  return _internal_has_eyes();
}
inline void Head::clear_eyes() {
  if (GetArenaForAllocation() == nullptr && eyes_ != nullptr) {
    delete eyes_;
  }
  eyes_ = nullptr;
}
inline const ::metahuman_service_api::Eyes& Head::_internal_eyes() const {
  const ::metahuman_service_api::Eyes* p = eyes_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Eyes&>(
      ::metahuman_service_api::_Eyes_default_instance_);
}
inline const ::metahuman_service_api::Eyes& Head::eyes() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Head.eyes)
  return _internal_eyes();
}
inline void Head::unsafe_arena_set_allocated_eyes(
    ::metahuman_service_api::Eyes* eyes) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(eyes_);
  }
  eyes_ = eyes;
  if (eyes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Head.eyes)
}
inline ::metahuman_service_api::Eyes* Head::release_eyes() {
  
  ::metahuman_service_api::Eyes* temp = eyes_;
  eyes_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Eyes* Head::unsafe_arena_release_eyes() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Head.eyes)
  
  ::metahuman_service_api::Eyes* temp = eyes_;
  eyes_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Eyes* Head::_internal_mutable_eyes() {
  
  if (eyes_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Eyes>(GetArenaForAllocation());
    eyes_ = p;
  }
  return eyes_;
}
inline ::metahuman_service_api::Eyes* Head::mutable_eyes() {
  ::metahuman_service_api::Eyes* _msg = _internal_mutable_eyes();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Head.eyes)
  return _msg;
}
inline void Head::set_allocated_eyes(::metahuman_service_api::Eyes* eyes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete eyes_;
  }
  if (eyes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Eyes>::GetOwningArena(eyes);
    if (message_arena != submessage_arena) {
      eyes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, eyes, submessage_arena);
    }
    
  } else {
    
  }
  eyes_ = eyes;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Head.eyes)
}

// .metahuman_service_api.Mesh teeth = 3;
inline bool Head::_internal_has_teeth() const {
  return this != internal_default_instance() && teeth_ != nullptr;
}
inline bool Head::has_teeth() const {
  return _internal_has_teeth();
}
inline void Head::clear_teeth() {
  if (GetArenaForAllocation() == nullptr && teeth_ != nullptr) {
    delete teeth_;
  }
  teeth_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Head::_internal_teeth() const {
  const ::metahuman_service_api::Mesh* p = teeth_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Head::teeth() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Head.teeth)
  return _internal_teeth();
}
inline void Head::unsafe_arena_set_allocated_teeth(
    ::metahuman_service_api::Mesh* teeth) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(teeth_);
  }
  teeth_ = teeth;
  if (teeth) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Head.teeth)
}
inline ::metahuman_service_api::Mesh* Head::release_teeth() {
  
  ::metahuman_service_api::Mesh* temp = teeth_;
  teeth_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::unsafe_arena_release_teeth() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Head.teeth)
  
  ::metahuman_service_api::Mesh* temp = teeth_;
  teeth_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::_internal_mutable_teeth() {
  
  if (teeth_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    teeth_ = p;
  }
  return teeth_;
}
inline ::metahuman_service_api::Mesh* Head::mutable_teeth() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_teeth();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Head.teeth)
  return _msg;
}
inline void Head::set_allocated_teeth(::metahuman_service_api::Mesh* teeth) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete teeth_;
  }
  if (teeth) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(teeth);
    if (message_arena != submessage_arena) {
      teeth = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, teeth, submessage_arena);
    }
    
  } else {
    
  }
  teeth_ = teeth;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Head.teeth)
}

// .metahuman_service_api.Mesh cartilage = 4;
inline bool Head::_internal_has_cartilage() const {
  return this != internal_default_instance() && cartilage_ != nullptr;
}
inline bool Head::has_cartilage() const {
  return _internal_has_cartilage();
}
inline void Head::clear_cartilage() {
  if (GetArenaForAllocation() == nullptr && cartilage_ != nullptr) {
    delete cartilage_;
  }
  cartilage_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Head::_internal_cartilage() const {
  const ::metahuman_service_api::Mesh* p = cartilage_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Head::cartilage() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Head.cartilage)
  return _internal_cartilage();
}
inline void Head::unsafe_arena_set_allocated_cartilage(
    ::metahuman_service_api::Mesh* cartilage) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cartilage_);
  }
  cartilage_ = cartilage;
  if (cartilage) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Head.cartilage)
}
inline ::metahuman_service_api::Mesh* Head::release_cartilage() {
  
  ::metahuman_service_api::Mesh* temp = cartilage_;
  cartilage_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::unsafe_arena_release_cartilage() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Head.cartilage)
  
  ::metahuman_service_api::Mesh* temp = cartilage_;
  cartilage_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::_internal_mutable_cartilage() {
  
  if (cartilage_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    cartilage_ = p;
  }
  return cartilage_;
}
inline ::metahuman_service_api::Mesh* Head::mutable_cartilage() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_cartilage();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Head.cartilage)
  return _msg;
}
inline void Head::set_allocated_cartilage(::metahuman_service_api::Mesh* cartilage) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete cartilage_;
  }
  if (cartilage) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(cartilage);
    if (message_arena != submessage_arena) {
      cartilage = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cartilage, submessage_arena);
    }
    
  } else {
    
  }
  cartilage_ = cartilage;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Head.cartilage)
}

// .metahuman_service_api.Mesh saliva = 5;
inline bool Head::_internal_has_saliva() const {
  return this != internal_default_instance() && saliva_ != nullptr;
}
inline bool Head::has_saliva() const {
  return _internal_has_saliva();
}
inline void Head::clear_saliva() {
  if (GetArenaForAllocation() == nullptr && saliva_ != nullptr) {
    delete saliva_;
  }
  saliva_ = nullptr;
}
inline const ::metahuman_service_api::Mesh& Head::_internal_saliva() const {
  const ::metahuman_service_api::Mesh* p = saliva_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Mesh&>(
      ::metahuman_service_api::_Mesh_default_instance_);
}
inline const ::metahuman_service_api::Mesh& Head::saliva() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Head.saliva)
  return _internal_saliva();
}
inline void Head::unsafe_arena_set_allocated_saliva(
    ::metahuman_service_api::Mesh* saliva) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(saliva_);
  }
  saliva_ = saliva;
  if (saliva) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.Head.saliva)
}
inline ::metahuman_service_api::Mesh* Head::release_saliva() {
  
  ::metahuman_service_api::Mesh* temp = saliva_;
  saliva_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::unsafe_arena_release_saliva() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Head.saliva)
  
  ::metahuman_service_api::Mesh* temp = saliva_;
  saliva_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Mesh* Head::_internal_mutable_saliva() {
  
  if (saliva_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Mesh>(GetArenaForAllocation());
    saliva_ = p;
  }
  return saliva_;
}
inline ::metahuman_service_api::Mesh* Head::mutable_saliva() {
  ::metahuman_service_api::Mesh* _msg = _internal_mutable_saliva();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Head.saliva)
  return _msg;
}
inline void Head::set_allocated_saliva(::metahuman_service_api::Mesh* saliva) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete saliva_;
  }
  if (saliva) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Mesh>::GetOwningArena(saliva);
    if (message_arena != submessage_arena) {
      saliva = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, saliva, submessage_arena);
    }
    
  } else {
    
  }
  saliva_ = saliva;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Head.saliva)
}

// -------------------------------------------------------------------

// Parameters

// repeated float bind_pose = 1;
inline int Parameters::_internal_bind_pose_size() const {
  return bind_pose_.size();
}
inline int Parameters::bind_pose_size() const {
  return _internal_bind_pose_size();
}
inline void Parameters::clear_bind_pose() {
  bind_pose_.Clear();
}
inline float Parameters::_internal_bind_pose(int index) const {
  return bind_pose_.Get(index);
}
inline float Parameters::bind_pose(int index) const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Parameters.bind_pose)
  return _internal_bind_pose(index);
}
inline void Parameters::set_bind_pose(int index, float value) {
  bind_pose_.Set(index, value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Parameters.bind_pose)
}
inline void Parameters::_internal_add_bind_pose(float value) {
  bind_pose_.Add(value);
}
inline void Parameters::add_bind_pose(float value) {
  _internal_add_bind_pose(value);
  // @@protoc_insertion_point(field_add:metahuman_service_api.Parameters.bind_pose)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Parameters::_internal_bind_pose() const {
  return bind_pose_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Parameters::bind_pose() const {
  // @@protoc_insertion_point(field_list:metahuman_service_api.Parameters.bind_pose)
  return _internal_bind_pose();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Parameters::_internal_mutable_bind_pose() {
  return &bind_pose_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Parameters::mutable_bind_pose() {
  // @@protoc_insertion_point(field_mutable_list:metahuman_service_api.Parameters.bind_pose)
  return _internal_mutable_bind_pose();
}

// repeated float solver_coefficients = 2;
inline int Parameters::_internal_solver_coefficients_size() const {
  return solver_coefficients_.size();
}
inline int Parameters::solver_coefficients_size() const {
  return _internal_solver_coefficients_size();
}
inline void Parameters::clear_solver_coefficients() {
  solver_coefficients_.Clear();
}
inline float Parameters::_internal_solver_coefficients(int index) const {
  return solver_coefficients_.Get(index);
}
inline float Parameters::solver_coefficients(int index) const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Parameters.solver_coefficients)
  return _internal_solver_coefficients(index);
}
inline void Parameters::set_solver_coefficients(int index, float value) {
  solver_coefficients_.Set(index, value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Parameters.solver_coefficients)
}
inline void Parameters::_internal_add_solver_coefficients(float value) {
  solver_coefficients_.Add(value);
}
inline void Parameters::add_solver_coefficients(float value) {
  _internal_add_solver_coefficients(value);
  // @@protoc_insertion_point(field_add:metahuman_service_api.Parameters.solver_coefficients)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Parameters::_internal_solver_coefficients() const {
  return solver_coefficients_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Parameters::solver_coefficients() const {
  // @@protoc_insertion_point(field_list:metahuman_service_api.Parameters.solver_coefficients)
  return _internal_solver_coefficients();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Parameters::_internal_mutable_solver_coefficients() {
  return &solver_coefficients_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Parameters::mutable_solver_coefficients() {
  // @@protoc_insertion_point(field_mutable_list:metahuman_service_api.Parameters.solver_coefficients)
  return _internal_mutable_solver_coefficients();
}

// string model_id = 3;
inline void Parameters::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& Parameters::model_id() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Parameters.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Parameters::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:metahuman_service_api.Parameters.model_id)
}
inline std::string* Parameters::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.Parameters.model_id)
  return _s;
}
inline const std::string& Parameters::_internal_model_id() const {
  return model_id_.Get();
}
inline void Parameters::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Parameters::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Parameters::release_model_id() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.Parameters.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Parameters::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.Parameters.model_id)
}

// -------------------------------------------------------------------

// Quality

// .metahuman_service_api.RigType rig_type = 1;
inline void Quality::clear_rig_type() {
  rig_type_ = 0;
}
inline ::metahuman_service_api::RigType Quality::_internal_rig_type() const {
  return static_cast< ::metahuman_service_api::RigType >(rig_type_);
}
inline ::metahuman_service_api::RigType Quality::rig_type() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Quality.rig_type)
  return _internal_rig_type();
}
inline void Quality::_internal_set_rig_type(::metahuman_service_api::RigType value) {
  
  rig_type_ = value;
}
inline void Quality::set_rig_type(::metahuman_service_api::RigType value) {
  _internal_set_rig_type(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Quality.rig_type)
}

// .metahuman_service_api.RefinementLevel refinement_level = 2;
inline void Quality::clear_refinement_level() {
  refinement_level_ = 0;
}
inline ::metahuman_service_api::RefinementLevel Quality::_internal_refinement_level() const {
  return static_cast< ::metahuman_service_api::RefinementLevel >(refinement_level_);
}
inline ::metahuman_service_api::RefinementLevel Quality::refinement_level() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Quality.refinement_level)
  return _internal_refinement_level();
}
inline void Quality::_internal_set_refinement_level(::metahuman_service_api::RefinementLevel value) {
  
  refinement_level_ = value;
}
inline void Quality::set_refinement_level(::metahuman_service_api::RefinementLevel value) {
  _internal_set_refinement_level(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Quality.refinement_level)
}

// .metahuman_service_api.ExportLayers export_layers = 3;
inline void Quality::clear_export_layers() {
  export_layers_ = 0;
}
inline ::metahuman_service_api::ExportLayers Quality::_internal_export_layers() const {
  return static_cast< ::metahuman_service_api::ExportLayers >(export_layers_);
}
inline ::metahuman_service_api::ExportLayers Quality::export_layers() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.Quality.export_layers)
  return _internal_export_layers();
}
inline void Quality::_internal_set_export_layers(::metahuman_service_api::ExportLayers value) {
  
  export_layers_ = value;
}
inline void Quality::set_export_layers(::metahuman_service_api::ExportLayers value) {
  _internal_set_export_layers(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.Quality.export_layers)
}

// -------------------------------------------------------------------

// UEVersion

// uint32 major = 1;
inline void UEVersion::clear_major() {
  major_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UEVersion::_internal_major() const {
  return major_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UEVersion::major() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.UEVersion.major)
  return _internal_major();
}
inline void UEVersion::_internal_set_major(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  major_ = value;
}
inline void UEVersion::set_major(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_major(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.UEVersion.major)
}

// uint32 minor = 2;
inline void UEVersion::clear_minor() {
  minor_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UEVersion::_internal_minor() const {
  return minor_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 UEVersion::minor() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.UEVersion.minor)
  return _internal_minor();
}
inline void UEVersion::_internal_set_minor(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  minor_ = value;
}
inline void UEVersion::set_minor(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_minor(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.UEVersion.minor)
}

// -------------------------------------------------------------------

// AutorigRequest

// .metahuman_service_api.Head head = 1;
inline bool AutorigRequest::_internal_has_head() const {
  return this != internal_default_instance() && head_ != nullptr;
}
inline bool AutorigRequest::has_head() const {
  return _internal_has_head();
}
inline void AutorigRequest::clear_head() {
  if (GetArenaForAllocation() == nullptr && head_ != nullptr) {
    delete head_;
  }
  head_ = nullptr;
}
inline const ::metahuman_service_api::Head& AutorigRequest::_internal_head() const {
  const ::metahuman_service_api::Head* p = head_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Head&>(
      ::metahuman_service_api::_Head_default_instance_);
}
inline const ::metahuman_service_api::Head& AutorigRequest::head() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.head)
  return _internal_head();
}
inline void AutorigRequest::unsafe_arena_set_allocated_head(
    ::metahuman_service_api::Head* head) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(head_);
  }
  head_ = head;
  if (head) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.AutorigRequest.head)
}
inline ::metahuman_service_api::Head* AutorigRequest::release_head() {
  
  ::metahuman_service_api::Head* temp = head_;
  head_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Head* AutorigRequest::unsafe_arena_release_head() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.AutorigRequest.head)
  
  ::metahuman_service_api::Head* temp = head_;
  head_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Head* AutorigRequest::_internal_mutable_head() {
  
  if (head_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Head>(GetArenaForAllocation());
    head_ = p;
  }
  return head_;
}
inline ::metahuman_service_api::Head* AutorigRequest::mutable_head() {
  ::metahuman_service_api::Head* _msg = _internal_mutable_head();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.AutorigRequest.head)
  return _msg;
}
inline void AutorigRequest::set_allocated_head(::metahuman_service_api::Head* head) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete head_;
  }
  if (head) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Head>::GetOwningArena(head);
    if (message_arena != submessage_arena) {
      head = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, head, submessage_arena);
    }
    
  } else {
    
  }
  head_ = head;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.AutorigRequest.head)
}

// int32 high_frequency_index = 2;
inline void AutorigRequest::clear_high_frequency_index() {
  high_frequency_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AutorigRequest::_internal_high_frequency_index() const {
  return high_frequency_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 AutorigRequest::high_frequency_index() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.high_frequency_index)
  return _internal_high_frequency_index();
}
inline void AutorigRequest::_internal_set_high_frequency_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  high_frequency_index_ = value;
}
inline void AutorigRequest::set_high_frequency_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_high_frequency_index(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.AutorigRequest.high_frequency_index)
}

// float to_target_scale = 3;
inline void AutorigRequest::clear_to_target_scale() {
  to_target_scale_ = 0;
}
inline float AutorigRequest::_internal_to_target_scale() const {
  return to_target_scale_;
}
inline float AutorigRequest::to_target_scale() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.to_target_scale)
  return _internal_to_target_scale();
}
inline void AutorigRequest::_internal_set_to_target_scale(float value) {
  
  to_target_scale_ = value;
}
inline void AutorigRequest::set_to_target_scale(float value) {
  _internal_set_to_target_scale(value);
  // @@protoc_insertion_point(field_set:metahuman_service_api.AutorigRequest.to_target_scale)
}

// optional .metahuman_service_api.Parameters parameters = 4;
inline bool AutorigRequest::_internal_has_parameters() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || parameters_ != nullptr);
  return value;
}
inline bool AutorigRequest::has_parameters() const {
  return _internal_has_parameters();
}
inline void AutorigRequest::clear_parameters() {
  if (parameters_ != nullptr) parameters_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::metahuman_service_api::Parameters& AutorigRequest::_internal_parameters() const {
  const ::metahuman_service_api::Parameters* p = parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Parameters&>(
      ::metahuman_service_api::_Parameters_default_instance_);
}
inline const ::metahuman_service_api::Parameters& AutorigRequest::parameters() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.parameters)
  return _internal_parameters();
}
inline void AutorigRequest::unsafe_arena_set_allocated_parameters(
    ::metahuman_service_api::Parameters* parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(parameters_);
  }
  parameters_ = parameters;
  if (parameters) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.AutorigRequest.parameters)
}
inline ::metahuman_service_api::Parameters* AutorigRequest::release_parameters() {
  _has_bits_[0] &= ~0x00000001u;
  ::metahuman_service_api::Parameters* temp = parameters_;
  parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Parameters* AutorigRequest::unsafe_arena_release_parameters() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.AutorigRequest.parameters)
  _has_bits_[0] &= ~0x00000001u;
  ::metahuman_service_api::Parameters* temp = parameters_;
  parameters_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Parameters* AutorigRequest::_internal_mutable_parameters() {
  _has_bits_[0] |= 0x00000001u;
  if (parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Parameters>(GetArenaForAllocation());
    parameters_ = p;
  }
  return parameters_;
}
inline ::metahuman_service_api::Parameters* AutorigRequest::mutable_parameters() {
  ::metahuman_service_api::Parameters* _msg = _internal_mutable_parameters();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.AutorigRequest.parameters)
  return _msg;
}
inline void AutorigRequest::set_allocated_parameters(::metahuman_service_api::Parameters* parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete parameters_;
  }
  if (parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Parameters>::GetOwningArena(parameters);
    if (message_arena != submessage_arena) {
      parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, parameters, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  parameters_ = parameters;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.AutorigRequest.parameters)
}

// .metahuman_service_api.Quality quality = 5;
inline bool AutorigRequest::_internal_has_quality() const {
  return this != internal_default_instance() && quality_ != nullptr;
}
inline bool AutorigRequest::has_quality() const {
  return _internal_has_quality();
}
inline void AutorigRequest::clear_quality() {
  if (GetArenaForAllocation() == nullptr && quality_ != nullptr) {
    delete quality_;
  }
  quality_ = nullptr;
}
inline const ::metahuman_service_api::Quality& AutorigRequest::_internal_quality() const {
  const ::metahuman_service_api::Quality* p = quality_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::Quality&>(
      ::metahuman_service_api::_Quality_default_instance_);
}
inline const ::metahuman_service_api::Quality& AutorigRequest::quality() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.quality)
  return _internal_quality();
}
inline void AutorigRequest::unsafe_arena_set_allocated_quality(
    ::metahuman_service_api::Quality* quality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(quality_);
  }
  quality_ = quality;
  if (quality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.AutorigRequest.quality)
}
inline ::metahuman_service_api::Quality* AutorigRequest::release_quality() {
  
  ::metahuman_service_api::Quality* temp = quality_;
  quality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::Quality* AutorigRequest::unsafe_arena_release_quality() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.AutorigRequest.quality)
  
  ::metahuman_service_api::Quality* temp = quality_;
  quality_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::Quality* AutorigRequest::_internal_mutable_quality() {
  
  if (quality_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::Quality>(GetArenaForAllocation());
    quality_ = p;
  }
  return quality_;
}
inline ::metahuman_service_api::Quality* AutorigRequest::mutable_quality() {
  ::metahuman_service_api::Quality* _msg = _internal_mutable_quality();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.AutorigRequest.quality)
  return _msg;
}
inline void AutorigRequest::set_allocated_quality(::metahuman_service_api::Quality* quality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete quality_;
  }
  if (quality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::Quality>::GetOwningArena(quality);
    if (message_arena != submessage_arena) {
      quality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quality, submessage_arena);
    }
    
  } else {
    
  }
  quality_ = quality;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.AutorigRequest.quality)
}

// .metahuman_service_api.UEVersion ue_version = 6;
inline bool AutorigRequest::_internal_has_ue_version() const {
  return this != internal_default_instance() && ue_version_ != nullptr;
}
inline bool AutorigRequest::has_ue_version() const {
  return _internal_has_ue_version();
}
inline void AutorigRequest::clear_ue_version() {
  if (GetArenaForAllocation() == nullptr && ue_version_ != nullptr) {
    delete ue_version_;
  }
  ue_version_ = nullptr;
}
inline const ::metahuman_service_api::UEVersion& AutorigRequest::_internal_ue_version() const {
  const ::metahuman_service_api::UEVersion* p = ue_version_;
  return p != nullptr ? *p : reinterpret_cast<const ::metahuman_service_api::UEVersion&>(
      ::metahuman_service_api::_UEVersion_default_instance_);
}
inline const ::metahuman_service_api::UEVersion& AutorigRequest::ue_version() const {
  // @@protoc_insertion_point(field_get:metahuman_service_api.AutorigRequest.ue_version)
  return _internal_ue_version();
}
inline void AutorigRequest::unsafe_arena_set_allocated_ue_version(
    ::metahuman_service_api::UEVersion* ue_version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ue_version_);
  }
  ue_version_ = ue_version;
  if (ue_version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metahuman_service_api.AutorigRequest.ue_version)
}
inline ::metahuman_service_api::UEVersion* AutorigRequest::release_ue_version() {
  
  ::metahuman_service_api::UEVersion* temp = ue_version_;
  ue_version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metahuman_service_api::UEVersion* AutorigRequest::unsafe_arena_release_ue_version() {
  // @@protoc_insertion_point(field_release:metahuman_service_api.AutorigRequest.ue_version)
  
  ::metahuman_service_api::UEVersion* temp = ue_version_;
  ue_version_ = nullptr;
  return temp;
}
inline ::metahuman_service_api::UEVersion* AutorigRequest::_internal_mutable_ue_version() {
  
  if (ue_version_ == nullptr) {
    auto* p = CreateMaybeMessage<::metahuman_service_api::UEVersion>(GetArenaForAllocation());
    ue_version_ = p;
  }
  return ue_version_;
}
inline ::metahuman_service_api::UEVersion* AutorigRequest::mutable_ue_version() {
  ::metahuman_service_api::UEVersion* _msg = _internal_mutable_ue_version();
  // @@protoc_insertion_point(field_mutable:metahuman_service_api.AutorigRequest.ue_version)
  return _msg;
}
inline void AutorigRequest::set_allocated_ue_version(::metahuman_service_api::UEVersion* ue_version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete ue_version_;
  }
  if (ue_version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metahuman_service_api::UEVersion>::GetOwningArena(ue_version);
    if (message_arena != submessage_arena) {
      ue_version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ue_version, submessage_arena);
    }
    
  } else {
    
  }
  ue_version_ = ue_version;
  // @@protoc_insertion_point(field_set_allocated:metahuman_service_api.AutorigRequest.ue_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace metahuman_service_api

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::metahuman_service_api::RigType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::metahuman_service_api::RigType>() {
  return ::metahuman_service_api::RigType_descriptor();
}
template <> struct is_proto_enum< ::metahuman_service_api::RefinementLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::metahuman_service_api::RefinementLevel>() {
  return ::metahuman_service_api::RefinementLevel_descriptor();
}
template <> struct is_proto_enum< ::metahuman_service_api::ExportLayers> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::metahuman_service_api::ExportLayers>() {
  return ::metahuman_service_api::ExportLayers_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_metahuman_5fservice_5fapi_2eproto
