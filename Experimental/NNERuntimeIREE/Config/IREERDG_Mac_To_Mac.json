{"ImporterCommand": ["${PLUGIN_DIR}/Binaries/ThirdParty/IREE/Mac/torch-mlir-import-onnx"], "ImporterArguments": "-o ${OUTPUT_PATH} --opset-version 21 ${INPUT_PATH}", "CompilerCommand": ["${PLUGIN_DIR}/Binaries/ThirdParty/IREE/Mac/iree-compile"], "Targets": [{"ShaderPlatform": "METAL_SM6", "CompilerArguments": "--iree-hal-target-backends=unreal-shader --iree-unreal-target=sm_75 --iree-hal-dump-executable-binaries-to=${BINARIES_PATH} -o ${VMFB_PATH} ${INPUT_PATH}"}]}