# UE5.6游戏项目开发守则

## 项目概述

### 核心目标
- 基于UE5.6最新功能实现大世界地图生成和性能优化系统
- 严格遵循ECS架构（Mass Entity系统）和数据驱动设计
- 集成ComputeFramework、FStreamableManager等UE5.6原生组件
- 使用现代化C++17/20特性和单一职责原则
### 技术栈要求
- **引擎版本**: Unreal Engine 5.6
- **C++标准**: C++17/20 (UE5.6支持的最新标准)
- **核心插件**: MassGameplay, ComputeFramework, ModelingToolsEditorMode
- **核心模块**: Engine, CoreUObject, AssetRegistry, MassEntity, MassSpawner

## 🎯 核心架构设计模式强制要求

**八大设计模式必须根据具体场景选择并集成：**

1. **MVVM模式** - UI数据绑定、实时界面更新、蓝图C++混合开发
2. **命令模式** - 复杂操作序列、撤销重做、批处理、延迟执行
3. **事件驱动模式** - 组件间松耦合通信、异步数据更新、多对多通信
4. **数据驱动架构** - 大规模数据处理、配置驱动逻辑、运行时数据管理
5. **可扩展组件模式** - 插件化功能、模块化扩展、运行时组件加载
6. **工厂模式** - 对象创建管理、类型安全实例化、配置驱动创建
7. **抽象工厂模式** - 跨平台适配、多套实现方案、运行时策略切换
8. **观察者模式** - 状态变化通知、数据同步、响应式编程

**集成要求**: 所有模式必须与UE5.6原生系统深度集成（Mass Entity、委托系统、Subsystem架构、Performance系统）

## UE5.6功能使用强制规范

### 智能指针使用规范

#### ✅ 必须使用
```cpp
// UE对象指针 - UE5.6推荐
TObjectPtr<UObject> MyObject;
TWeakObjectPtr<AActor> WeakActor;

// 共享指针
TSharedPtr<FMyClass> SharedData;
TUniquePtr<FMyClass> UniqueData;
```

#### ❌ 严格禁止
```cpp
// 原始指针 - 违反项目规范
UObject* RawPointer;  // 禁止
AActor* ActorPtr;     // 禁止

// 手动内存管理 - 违反UE5.6最佳实践
FMyClass* ManualPtr = new FMyClass();  // 禁止
```

## 代码重复检查和清理规范

### 🔍 实现前强制检查流程
1. **UE5.6 API搜索**：使用`resolve-library-id`和`get-library-docs`工具检查UE引擎是否已有相同功能
2. **项目代码搜索**：使用`codebase-retrieval`工具搜索项目中是否存在类似实现
3. **Mass Entity检查**：优先检查Mass Components和Processors是否已有相关功能
4. **Performance系统检查**：检查`Source/Game/Public/Performance`目录下是否已有相关功能

### 🧹 代码清理强制要求
```cpp
// ❌ 必须删除的废弃代码示例
// 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

// ❌ 必须删除的重复实现
class FUnifiedObjectPool { /* 重复实现，必须删除 */ };
class FTieredObjectPool { /* 保留更优实现 */ };

// ✅ 保留的优化实现
template<typename T>
class FTieredObjectPool {
    // 三层存储：Hot/Warm/Cold
};
```

### 📝 增量更新规范
```cpp
// ✅ 正确的增量更新方式
void ExistingFunction(int32 NewParam = 0) // 添加默认参数保持兼容性
{
    // 保留原有逻辑
    OriginalLogic();

    // 添加新功能
    if (NewParam > 0) {
        NewFeatureLogic(NewParam);
    }
}

// ❌ 禁止完全重写
void ExistingFunction() // 不要删除原有实现重新写
{
    // 完全新的实现 - 禁止这样做
}
```

### UE引擎功能检查规范

#### 🔍 代码实现前必须检查
- **搜索UE5.6 API文档**：确认引擎是否已有相同功能
- **检查Mass Entity系统**：优先使用Mass Components和Processors
- **检查ComputeFramework**：GPU计算任务必须使用ComputeFramework
- **检查现有Subsystem**：继承现有UWorldSubsystem架构

## 🚀 Mass Entity ECS最佳实践

> **系统基础**: 项目已启用MassGameplay插件，支持UE5.6最新ECS特性
> **核心价值**: 大规模实体处理、数据驱动架构、批处理优化、内存友好设计
> **适用场景**: 大量游戏对象、AI系统、粒子系统、程序化生成

### 🧩 Fragment设计原则和最佳实践

#### ✅ Fragment数据结构设计规范
```cpp
// 【Fragment设计原则】数据密集、逻辑分离、缓存友好
USTRUCT()
struct GAME_API FPerformanceFragment : public FMassFragment
{
    GENERATED_BODY()

    // 【内存布局优化】按大小降序排列，减少内存填充
    UPROPERTY()
    double LastUpdateTime = 0.0;        // 8字节

    UPROPERTY()
    float OptimizationLevel = 1.0f;     // 4字节

    UPROPERTY()
    int32 CacheHitCount = 0;            // 4字节

    UPROPERTY()
    uint8 ProcessorFlags = 0;           // 1字节

    UPROPERTY()
    bool bIsOptimized = false;          // 1字节

    // 【数据验证】Fragment数据完整性检查
    bool IsValid() const
    {
        return OptimizationLevel >= 0.0f && CacheHitCount >= 0;
    }

    // 【批处理友好】简单的数据操作，避免复杂逻辑
    void UpdateOptimizationLevel(float DeltaLevel)
    {
        OptimizationLevel = FMath::Clamp(OptimizationLevel + DeltaLevel, 0.0f, 10.0f);
    }
};

// 【组合Fragment】相关数据分组，提高缓存局部性
USTRUCT()
struct GAME_API FTransformFragment : public FMassFragment
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Location = FVector::ZeroVector;

    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    FVector Scale = FVector::OneVector;

    // 【快速访问】提供常用的变换操作
    FTransform GetTransform() const
    {
        return FTransform(Rotation, Location, Scale);
    }

    void SetTransform(const FTransform& NewTransform)
    {
        Location = NewTransform.GetLocation();
        Rotation = NewTransform.GetRotation().Rotator();
        Scale = NewTransform.GetScale3D();
    }
};

// 【标签Fragment】轻量级分类和过滤
USTRUCT()
struct GAME_API FOptimizedEntityTag : public FMassTag
{
    GENERATED_BODY()
    // 空结构体，仅用于标记和查询过滤
};
```

### Processor批处理优化
```cpp
// 【高性能Processor实现】
UCLASS()
class GAME_API UPerformanceProcessor : public UMassProcessor
{
    GENERATED_BODY()

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override
    {
        EntityQuery.AddRequirement<FPerformanceFragment>(EMassFragmentAccess::ReadWrite);
        EntityQuery.AddRequirement<FTransformFragment>(EMassFragmentAccess::ReadOnly);
        EntityQuery.AddTagRequirement<FOptimizedEntityTag>(EMassFragmentPresence::All);
    }

    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        EntityQuery.ForEachEntityChunk(EntityManager, Context,
            [this](FMassExecutionContext& Context)
            {
                const TArrayView<FPerformanceFragment> PerformanceFragments =
                    Context.GetMutableFragmentView<FPerformanceFragment>();
                const TArrayView<FTransformFragment> TransformFragments =
                    Context.GetFragmentView<FTransformFragment>();

                const int32 NumEntities = Context.GetNumEntities();
                for (int32 EntityIndex = 0; EntityIndex < NumEntities; ++EntityIndex)
                {
                    ProcessSingleEntity(PerformanceFragments[EntityIndex],
                                      TransformFragments[EntityIndex]);
                }
            });
    }

private:
    FORCEINLINE void ProcessSingleEntity(FPerformanceFragment& Performance,
                                        const FTransformFragment& Transform)
    {
        const float DistanceFromOrigin = Transform.Location.Size();
        const float OptimizationBonus = FMath::Clamp(1.0f / (1.0f + DistanceFromOrigin * 0.001f), 0.1f, 2.0f);
        Performance.UpdateOptimizationLevel(OptimizationBonus * GetWorld()->GetDeltaSeconds());
    }

    FMassEntityQuery EntityQuery;
};
```

```

### 内存布局优化
```cpp
// 【实体池管理】预分配实体池，避免运行时分配
UCLASS()
class GAME_API UMassEntityPoolManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    FMassEntityHandle GetPooledEntity()
    {
        if (EntityPool.Num() > 0)
        {
            return EntityPool.Pop();
        }

        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();
            return EntityManager.CreateEntity();
        }

        return FMassEntityHandle();
    }

    void ReturnEntityToPool(FMassEntityHandle Entity)
    {
        if (Entity.IsValid())
        {
            EntityPool.Add(Entity);
        }
    }

private:
    UPROPERTY()
    TArray<FMassEntityHandle> EntityPool;
};
```
```

## Performance系统强制使用规范

### 核心Performance组件
**必须优先使用`Source/Game/Public/Performance`文件夹中的现有组件：**
- **PerformanceOptimizer.h/.cpp** - 主性能优化器，协调所有性能子系统
- **ObjectPoolManager.h/.cpp** - 对象池管理器，提供分层对象池功能
- **CacheManager.h/.cpp** - 缓存管理器，提供多级缓存系统
- **GPUComputeManager.h/.cpp** - GPU计算管理器，管理ComputeFramework

### Performance系统使用时机判断
**强制使用场景：**
1. **大量对象创建/销毁** - 超过100个同类型对象时必须使用ObjectPoolManager
2. **频繁数据访问** - 同一数据在1秒内访问超过10次时必须使用CacheManager
3. **计算密集型任务** - CPU使用率超过70%时必须考虑GPUComputeManager
4. **内存使用超过阈值** - 堆内存使用超过1GB时必须使用Performance系统优化
5. **Mass Entity批处理** - 处理超过1000个实体时必须使用PerformanceProcessor

```

## 单一职责原则强制规范

### 类职责划分强制要求
```cpp
// ✅ 正确的单一职责设计
class GAME_API UMapGenerator : public UObject
{
    // 只负责地图生成逻辑
public:
    void GenerateMap(const FMapConfig& Config);
private:
    void GenerateHeightmap();
    void GenerateBiomes();
};

class GAME_API UMapRenderer : public UObject
{
    // 只负责地图渲染
public:
    void RenderMap(const FMapData& MapData);
private:
    void CreateMeshes();
    void ApplyMaterials();
};

// ❌ 违反单一职责的设计
class UMapSystem : public UObject
{
    // 违反：同时负责生成、渲染、保存、加载
    void GenerateMap();
    void RenderMap();
    void SaveMap();
    void LoadMap();
};
```
```

## UE5.6最新功能强制使用规范

### 必须优先使用的UE5.6功能
```cpp
// ✅ Instanced Actors（大量对象渲染优化）
UCLASS()
class GAME_API UInstancedActorManager : public UWorldSubsystem
{
public:
    void CreateInstancedActors(TSubclassOf<AActor> ActorClass, const TArray<FTransform>& Transforms);
};

// ✅ 最新异步API
AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []() {
    // 后台任务
});

// ✅ ComputeFramework深度集成
UCLASS()
class GAME_API UMyComputeGraph : public UComputeGraph
{
    // GPU计算图定义
};

// ❌ 禁止使用过时功能
Async(EAsyncExecution::ThreadPool, []() {}); // 禁止使用
UObject* Asset = LoadObject<UObject>(nullptr, TEXT("/Path/To/Asset")); // 禁止同步加载
```

## 🏢 Splash Damage企业级编程规范

### 头文件组织和IWYU原则
```cpp
// 【严格的头文件包含顺序】
#pragma once

// 1. IWYU原则 - 只包含实际使用的头文件
#include "CoreMinimal.h"

// 2. 引擎依赖，按逻辑分组
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "GameFramework/Character.h"

// 3. 项目特定头文件
#include "Performance/CacheManager.h"
#include "LocalUtility.h"

// 4. 生成的头文件（必须最后）
#include "ClassName.generated.h"
```

### 前向声明和循环依赖避免
```cpp
// 【前向声明最佳实践】
class UPerformanceOptimizer;
class UCacheManager;
struct FPerformanceMetrics;
enum class ECacheLevel : uint8;
```

### 企业级内存管理规范
```cpp
// ✅ 智能指针使用规范
TObjectPtr<UObject> MyObject;        // UE对象指针
TWeakObjectPtr<AActor> WeakActor;    // 弱引用
TSharedPtr<FPerformanceData> SharedData;  // 共享指针
TUniquePtr<FPerformanceData> UniqueData;  // 唯一指针
```

```

## 🎨 Allar风格指南集成

### 资源命名约定规范
**基础资源命名模式：`Prefix_BaseAssetName_Variant_Suffix`**

#### 资源类型前缀表（基于Allar标准）
| 资源类型 | 前缀 | 示例 |
|---------|------|------|
| 蓝图类 | BP_ | BP_PlayerCharacter |
| 材质 | M_ | M_Brick_Clay_Orange |
| 材质实例 | MI_ | MI_Brick_Clay_Orange |
| 贴图 | T_ | T_Brick_Diffuse |
| 静态网格 | SM_ | SM_Rock_01 |
| 骨骼网格 | SK_ | SK_Character |
| 动画蓝图 | ABP_ | ABP_Character |
| 动画序列 | A_ | A_Character_Run |
| 音效 | A_ | A_Character_Footstep_01 |
| 粒子系统 | PS_ | PS_Fire |
| 关卡 | L_ | L_Persistent |

### Content目录结构规范（基于项目现状优化）
```
Content/
├── Art/
│   ├── Characters/
│   ├── Environment/
│   ├── Materials/
│   └── Textures/
├── Audio/
├── Blueprints/
│   ├── Characters/
│   ├── GameMode/
│   └── UI/
├── Data/
├── Levels/
└── UI/
```

### 蓝图开发最佳实践
```cpp
// ✅ 蓝图变量命名规范
UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
float WalkSpeed = 600.0f;

UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
bool bCanAttack = true;

// ✅ 蓝图函数命名规范
UFUNCTION(BlueprintCallable, Category = "Combat")
void StartAttack();

UFUNCTION(BlueprintImplementableEvent, Category = "Combat")
void OnAttackFinished();
```

## 錯誤處理規範

### 必須使用UE5.6錯誤處理機制
```cpp
// ✅ 使用UE日志系统
UE_LOG(LogTemp, Warning, TEXT("性能警告: %s"), *ErrorMessage);

// ✅ 使用check宏进行断言
check(IsValid(MyActor));
checkf(Index >= 0, TEXT("索引不能为负数: %d"), Index);

// ✅ 使用ensure进行软断言
if (!ensure(IsValid(Component)))
{
    return; // 优雅处理错误
}
```

### ❌ 禁止使用標準C++異常
```cpp
// 禁止使用try/catch
try { /* 代码 */ } catch(...) { /* 处理 */ } // 违反UE规范

// 禁止抛出异常
throw std::runtime_error("错误"); // 违反UE规范
```

## 禁止事項

### 絕對禁止的編程實踐

#### ❌ 內存管理禁止事項
- 使用原始指針管理UE對象
- 手動調用new/delete
- 在構造函數中進行複雜初始化
- 在析構函數中訪問UE對象

#### ❌ 異步編程禁止事項
- 在非主線程直接操作UE對象
- 忽略線程安全問題
- 使用過時的異步API
- 阻塞主線程進行長時間操作

#### ❌ 性能優化禁止事項
- 在Tick函數中進行重型計算
- 忽略LOD系統
- 創建自定義對象池（必須使用Performance系统）
- 忽略內存對齊

#### ❌ 代码结构禁止事项
- 在头文件中包含大型实现
- 循环依赖
- 违反单一职责原则
- 忽略IWYU原则
```

## 项目逻辑一致性和设计意图保证

### 设计意图强制检查清单
**每次代码修改必须验证：**
1. **八大设计模式集成**：所有新功能必须选择并正确集成至少一个核心设计模式
2. **数据驱动设计**：所有配置必须通过UDataAsset管理
3. **单一职责原则**：每个类/函数只负责一个明确的职责
4. **性能优化目标**：所有修改必须考虑性能影响
5. **UE5.6功能优先**：优先使用引擎最新功能而非自定义实现

### AI Agent决策优先级

#### 核心架构决策优先级
1. **UE5.6原生功能** > 自定义实现
2. **Mass Entity系统** > 传统Actor系统
3. **Performance系统对象池** > 直接内存分配
4. **数据驱动配置** > 硬编码值
5. **增量更新** > 完全重写

#### 八大设计模式优先级排序
1. **数据驱动架构** > 硬编码逻辑
2. **Mass Entity ECS** > 传统组件系统
3. **事件驱动模式** > 直接函数调用
4. **MVVM模式** > 传统UI绑定
5. **命令模式** > 直接操作
6. **观察者模式** > 轮询检查
7. **工厂模式** > 直接构造
8. **抽象工厂模式** > 条件分支

### 八大设计模式智能选择决策树
```
需要添加新功能？
├─ UI相关？
│  ├─ 复杂数据绑定？ → **MVVM模式** + UE5.6 ModelViewViewModel
│  ├─ 简单显示？ → 直接UMG + 数据驱动配置
│  └─ 实时更新？ → **观察者模式** + **事件驱动**
│
├─ 用户交互？
│  ├─ 复杂操作序列？ → **命令模式** + Enhanced Input
│  ├─ 撤销重做？ → **命令模式** + 历史管理
│  └─ 简单交互？ → 直接事件处理
│
├─ 数据管理？
│  ├─ 配置数据？ → **数据驱动架构** + UDataAsset
│  ├─ 大量实体？ → **Mass Entity系统** + ECS架构
│  └─ 简单数据？ → 直接配置 + Performance监控
│
├─ 对象创建管理？
│  ├─ 大量同类对象？ → **工厂模式** + ObjectPoolManager
│  ├─ 复杂创建逻辑？ → **抽象工厂模式** + 策略选择
│  └─ 简单创建？ → 直接构造 + 智能指针
│
└─ 系统通信？
   ├─ 松耦合通信？ → **事件驱动模式** + UE5.6委托
   ├─ 状态同步？ → **观察者模式** + 数据绑定
   └─ 直接调用？ → 接口模式 + 依赖注入
```

## 架构模式集成示例

### 综合示例：游戏状态管理系统
```cpp
// 【多模式协同】MVVM + 数据驱动 + 事件驱动 + 命令模式集成
UCLASS()
class GAME_API UGameStateManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【数据驱动】使用UDataAsset配置游戏状态
    UPROPERTY(EditAnywhere, Category = "Configuration")
    TObjectPtr<UGameStateConfigDataAsset> GameStateConfig;

    // 【MVVM集成】ViewModel用于UI数据绑定
    UPROPERTY(BlueprintReadOnly, Category = "MVVM")
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【事件驱动】状态变更事件
    DECLARE_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, EGameState, EGameState);
    FOnGameStateChanged OnGameStateChanged;

    // 【命令模式】状态变更命令
    UFUNCTION(BlueprintCallable, Category = "Game State")
    void ChangeGameState(EGameState NewState)
    {
        // 创建状态变更命令
        TObjectPtr<UGameStateChangeCommand> Command = NewObject<UGameStateChangeCommand>();
        Command->Initialize(CurrentState, NewState, this);

        // 执行命令
        CommandManager->ExecuteCommand(Command);
    }

private:
    EGameState CurrentState = EGameState::MainMenu;

    UPROPERTY()
    TObjectPtr<UCommandManager> CommandManager;
};
```

```

## 测试和验证规范

### 必须进行的测试类型
```cpp
// ✅ 性能测试要求
void TestPerformance()
{
    // 帧率测试：确保60FPS稳定运行
    // 内存测试：监控内存使用不超过预算
    // 批处理测试：验证Mass Entity批处理效率
}

// ✅ 稳定性测试要求
void TestStability()
{
    // 长时间运行测试
    // 内存泄漏检测
    // 异常情况处理验证
}
```








































