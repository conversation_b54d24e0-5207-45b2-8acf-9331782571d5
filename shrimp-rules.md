# UE5.6游戏项目开发守则

## 项目概述

### 核心目标
- 基于UE5.6最新功能实现大世界地图生成和性能优化系统
- 严格遵循ECS架构（Mass Entity系统）和数据驱动设计
- 集成ComputeFramework、FStreamableManager等UE5.6原生组件
- 使用现代化C++17/20特性和单一职责原则

### 🌟 集成企业级编程标准
本项目融合了三大业界领先的编程标准：

1. **Splash Damage企业级编程规范** - 企业级代码质量、团队协作、可维护性
2. **Allar风格指南** - 项目一致性、资源命名、目录结构
3. **Mass Entity最佳实践** - 大规模实体处理、性能优化、数据驱动架构

### 技术栈要求
- **引擎版本**: Unreal Engine 5.6
- **C++标准**: C++17/20 (UE5.6支持的最新标准)
- **核心插件**: MassGameplay, ComputeFramework, ModelingToolsEditorMode
- **核心模块**: Engine, CoreUObject, AssetRegistry, MassEntity, MassSpawner

## 🎯 核心架构设计模式强制要求

**八大设计模式必须根据具体场景选择并集成：**

1. **MVVM模式** - UI数据绑定、实时界面更新、蓝图C++混合开发
2. **命令模式** - 复杂操作序列、撤销重做、批处理、延迟执行
3. **事件驱动模式** - 组件间松耦合通信、异步数据更新、多对多通信
4. **数据驱动架构** - 大规模数据处理、配置驱动逻辑、运行时数据管理
5. **可扩展组件模式** - 插件化功能、模块化扩展、运行时组件加载
6. **工厂模式** - 对象创建管理、类型安全实例化、配置驱动创建
7. **抽象工厂模式** - 跨平台适配、多套实现方案、运行时策略切换
8. **观察者模式** - 状态变化通知、数据同步、响应式编程

**集成要求**: 所有模式必须与UE5.6原生系统深度集成（Mass Entity、委托系统、Subsystem架构、Performance系统）

## UE5.6功能使用强制规范

### 智能指针使用规范

#### ✅ 必须使用
```cpp
// UE对象指针 - UE5.6推荐
TObjectPtr<UObject> MyObject;
TWeakObjectPtr<AActor> WeakActor;

// 共享指针
TSharedPtr<FMyClass> SharedData;
TUniquePtr<FMyClass> UniqueData;
```

#### ❌ 严格禁止
```cpp
// 原始指针 - 违反项目规范
UObject* RawPointer;  // 禁止
AActor* ActorPtr;     // 禁止

// 手动内存管理 - 违反UE5.6最佳实践
FMyClass* ManualPtr = new FMyClass();  // 禁止
```

## 代码重复检查和清理规范

### 🔍 实现前强制检查流程
1. **UE5.6 API搜索**：使用`resolve-library-id`和`get-library-docs`工具检查UE引擎是否已有相同功能
2. **项目代码搜索**：使用`codebase-retrieval`工具搜索项目中是否存在类似实现
3. **Mass Entity检查**：优先检查Mass Components和Processors是否已有相关功能
4. **Performance系统检查**：检查`Source/Game/Public/Performance`目录下是否已有相关功能

### 🧹 代码清理强制要求
```cpp
// ❌ 必须删除的废弃代码示例
// 【已删除】GetObjectPoolSize - 已被GetActiveObjectsCount替代

// ❌ 必须删除的重复实现
class FUnifiedObjectPool { /* 重复实现，必须删除 */ };
class FTieredObjectPool { /* 保留更优实现 */ };

// ✅ 保留的优化实现
template<typename T>
class FTieredObjectPool {
    // 三层存储：Hot/Warm/Cold
};
```

### 📝 增量更新规范
```cpp
// ✅ 正确的增量更新方式
void ExistingFunction(int32 NewParam = 0) // 添加默认参数保持兼容性
{
    // 保留原有逻辑
    OriginalLogic();

    // 添加新功能
    if (NewParam > 0) {
        NewFeatureLogic(NewParam);
    }
}

// ❌ 禁止完全重写
void ExistingFunction() // 不要删除原有实现重新写
{
    // 完全新的实现 - 禁止这样做
}
```

### UE引擎功能检查规范

#### 🔍 代码实现前必须检查
- **搜索UE5.6 API文档**：确认引擎是否已有相同功能
- **检查Mass Entity系统**：优先使用Mass Components和Processors
- **检查ComputeFramework**：GPU计算任务必须使用ComputeFramework
- **检查现有Subsystem**：继承现有UWorldSubsystem架构

## 🚀 Mass Entity ECS最佳实践

> **系统基础**: 项目已启用MassGameplay插件，支持UE5.6最新ECS特性
> **核心价值**: 大规模实体处理、数据驱动架构、批处理优化、内存友好设计
> **适用场景**: 大量游戏对象、AI系统、粒子系统、程序化生成

### 🧩 Fragment设计原则和最佳实践

#### ✅ Fragment数据结构设计规范
```cpp
// 【Fragment设计原则】数据密集、逻辑分离、缓存友好
USTRUCT()
struct GAME_API FPerformanceFragment : public FMassFragment
{
    GENERATED_BODY()

    // 【内存布局优化】按大小降序排列，减少内存填充
    UPROPERTY()
    double LastUpdateTime = 0.0;        // 8字节

    UPROPERTY()
    float OptimizationLevel = 1.0f;     // 4字节

    UPROPERTY()
    int32 CacheHitCount = 0;            // 4字节

    UPROPERTY()
    uint8 ProcessorFlags = 0;           // 1字节

    UPROPERTY()
    bool bIsOptimized = false;          // 1字节

    // 【数据验证】Fragment数据完整性检查
    bool IsValid() const
    {
        return OptimizationLevel >= 0.0f && CacheHitCount >= 0;
    }

    // 【批处理友好】简单的数据操作，避免复杂逻辑
    void UpdateOptimizationLevel(float DeltaLevel)
    {
        OptimizationLevel = FMath::Clamp(OptimizationLevel + DeltaLevel, 0.0f, 10.0f);
    }
};

// 【组合Fragment】相关数据分组，提高缓存局部性
USTRUCT()
struct GAME_API FTransformFragment : public FMassFragment
{
    GENERATED_BODY()

    UPROPERTY()
    FVector Location = FVector::ZeroVector;

    UPROPERTY()
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY()
    FVector Scale = FVector::OneVector;

    // 【快速访问】提供常用的变换操作
    FTransform GetTransform() const
    {
        return FTransform(Rotation, Location, Scale);
    }

    void SetTransform(const FTransform& NewTransform)
    {
        Location = NewTransform.GetLocation();
        Rotation = NewTransform.GetRotation().Rotator();
        Scale = NewTransform.GetScale3D();
    }
};

// 【标签Fragment】轻量级分类和过滤
USTRUCT()
struct GAME_API FOptimizedEntityTag : public FMassTag
{
    GENERATED_BODY()
    // 空结构体，仅用于标记和查询过滤
};
```

### Processor批处理优化
```cpp
// 【高性能Processor实现】
UCLASS()
class GAME_API UPerformanceProcessor : public UMassProcessor
{
    GENERATED_BODY()

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override
    {
        EntityQuery.AddRequirement<FPerformanceFragment>(EMassFragmentAccess::ReadWrite);
        EntityQuery.AddRequirement<FTransformFragment>(EMassFragmentAccess::ReadOnly);
        EntityQuery.AddTagRequirement<FOptimizedEntityTag>(EMassFragmentPresence::All);
    }

    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        EntityQuery.ForEachEntityChunk(EntityManager, Context,
            [this](FMassExecutionContext& Context)
            {
                const TArrayView<FPerformanceFragment> PerformanceFragments =
                    Context.GetMutableFragmentView<FPerformanceFragment>();
                const TArrayView<FTransformFragment> TransformFragments =
                    Context.GetFragmentView<FTransformFragment>();

                const int32 NumEntities = Context.GetNumEntities();
                for (int32 EntityIndex = 0; EntityIndex < NumEntities; ++EntityIndex)
                {
                    ProcessSingleEntity(PerformanceFragments[EntityIndex],
                                      TransformFragments[EntityIndex]);
                }
            });
    }

private:
    FORCEINLINE void ProcessSingleEntity(FPerformanceFragment& Performance,
                                        const FTransformFragment& Transform)
    {
        const float DistanceFromOrigin = Transform.Location.Size();
        const float OptimizationBonus = FMath::Clamp(1.0f / (1.0f + DistanceFromOrigin * 0.001f), 0.1f, 2.0f);
        Performance.UpdateOptimizationLevel(OptimizationBonus * GetWorld()->GetDeltaSeconds());
    }

    FMassEntityQuery EntityQuery;
};
```

```

### 内存布局优化
```cpp
// 【实体池管理】预分配实体池，避免运行时分配
UCLASS()
class GAME_API UMassEntityPoolManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    FMassEntityHandle GetPooledEntity()
    {
        if (EntityPool.Num() > 0)
        {
            return EntityPool.Pop();
        }

        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();
            return EntityManager.CreateEntity();
        }

        return FMassEntityHandle();
    }

    void ReturnEntityToPool(FMassEntityHandle Entity)
    {
        if (Entity.IsValid())
        {
            EntityPool.Add(Entity);
        }
    }

private:
    UPROPERTY()
    TArray<FMassEntityHandle> EntityPool;
};
```
```

## Performance系统强制使用规范

### 核心Performance组件
**必须优先使用`Source/Game/Public/Performance`文件夹中的现有组件：**
- **PerformanceOptimizer.h/.cpp** - 主性能优化器，协调所有性能子系统
- **ObjectPoolManager.h/.cpp** - 对象池管理器，提供分层对象池功能
- **CacheManager.h/.cpp** - 缓存管理器，提供多级缓存系统
- **GPUComputeManager.h/.cpp** - GPU计算管理器，管理ComputeFramework

### Performance系统使用时机判断
**强制使用场景：**
1. **大量对象创建/销毁** - 超过100个同类型对象时必须使用ObjectPoolManager
2. **频繁数据访问** - 同一数据在1秒内访问超过10次时必须使用CacheManager
3. **计算密集型任务** - CPU使用率超过70%时必须考虑GPUComputeManager
4. **内存使用超过阈值** - 堆内存使用超过1GB时必须使用Performance系统优化
5. **Mass Entity批处理** - 处理超过1000个实体时必须使用PerformanceProcessor

```

## 单一职责原则强制规范

### 类职责划分强制要求
```cpp
// ✅ 正确的单一职责设计
class GAME_API UMapGenerator : public UObject
{
    // 只负责地图生成逻辑
public:
    void GenerateMap(const FMapConfig& Config);
private:
    void GenerateHeightmap();
    void GenerateBiomes();
};

class GAME_API UMapRenderer : public UObject
{
    // 只负责地图渲染
public:
    void RenderMap(const FMapData& MapData);
private:
    void CreateMeshes();
    void ApplyMaterials();
};

// ❌ 违反单一职责的设计
class UMapSystem : public UObject
{
    // 违反：同时负责生成、渲染、保存、加载
    void GenerateMap();
    void RenderMap();
    void SaveMap();
    void LoadMap();
};
```
```

## UE5.6最新功能强制使用规范

### 必须优先使用的UE5.6功能
```cpp
// ✅ Instanced Actors（大量对象渲染优化）
UCLASS()
class GAME_API UInstancedActorManager : public UWorldSubsystem
{
public:
    void CreateInstancedActors(TSubclassOf<AActor> ActorClass, const TArray<FTransform>& Transforms);
};

// ✅ 最新异步API
AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []() {
    // 后台任务
});

// ✅ ComputeFramework深度集成
UCLASS()
class GAME_API UMyComputeGraph : public UComputeGraph
{
    // GPU计算图定义
};

// ❌ 禁止使用过时功能
Async(EAsyncExecution::ThreadPool, []() {}); // 禁止使用
UObject* Asset = LoadObject<UObject>(nullptr, TEXT("/Path/To/Asset")); // 禁止同步加载
```

## 🏢 Splash Damage企业级编程规范

### 头文件组织和IWYU原则
```cpp
// 【严格的头文件包含顺序】
#pragma once

// 1. IWYU原则 - 只包含实际使用的头文件
#include "CoreMinimal.h"

// 2. 引擎依赖，按逻辑分组
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
#include "GameFramework/Character.h"

// 3. 项目特定头文件
#include "Performance/CacheManager.h"
#include "LocalUtility.h"

// 4. 生成的头文件（必须最后）
#include "ClassName.generated.h"
```

### 前向声明和循环依赖避免
```cpp
// 【前向声明最佳实践】
class UPerformanceOptimizer;
class UCacheManager;
struct FPerformanceMetrics;
enum class ECacheLevel : uint8;
```

### 企业级内存管理规范
```cpp
// ✅ 智能指针使用规范
TObjectPtr<UObject> MyObject;        // UE对象指针
TWeakObjectPtr<AActor> WeakActor;    // 弱引用
TSharedPtr<FPerformanceData> SharedData;  // 共享指针
TUniquePtr<FPerformanceData> UniqueData;  // 唯一指针
```

```

## 🎨 Allar风格指南集成

### 资源命名约定规范
**基础资源命名模式：`Prefix_BaseAssetName_Variant_Suffix`**

#### 资源类型前缀表（基于Allar标准）
| 资源类型 | 前缀 | 示例 |
|---------|------|------|
| 蓝图类 | BP_ | BP_PlayerCharacter |
| 材质 | M_ | M_Brick_Clay_Orange |
| 材质实例 | MI_ | MI_Brick_Clay_Orange |
| 贴图 | T_ | T_Brick_Diffuse |
| 静态网格 | SM_ | SM_Rock_01 |
| 骨骼网格 | SK_ | SK_Character |
| 动画蓝图 | ABP_ | ABP_Character |
| 动画序列 | A_ | A_Character_Run |
| 音效 | A_ | A_Character_Footstep_01 |
| 粒子系统 | PS_ | PS_Fire |
| 关卡 | L_ | L_Persistent |

### Content目录结构规范（基于项目现状优化）
```
Content/
├── Art/
│   ├── Characters/
│   ├── Environment/
│   ├── Materials/
│   └── Textures/
├── Audio/
├── Blueprints/
│   ├── Characters/
│   ├── GameMode/
│   └── UI/
├── Data/
├── Levels/
└── UI/
```

### 蓝图开发最佳实践
```cpp
// ✅ 蓝图变量命名规范
UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
float WalkSpeed = 600.0f;

UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
bool bCanAttack = true;

// ✅ 蓝图函数命名规范
UFUNCTION(BlueprintCallable, Category = "Combat")
void StartAttack();

UFUNCTION(BlueprintImplementableEvent, Category = "Combat")
void OnAttackFinished();
```

## 錯誤處理規範

### 必須使用UE5.6錯誤處理機制
```cpp
// ✅ 使用UE日志系统
UE_LOG(LogTemp, Warning, TEXT("性能警告: %s"), *ErrorMessage);

// ✅ 使用check宏进行断言
check(IsValid(MyActor));
checkf(Index >= 0, TEXT("索引不能为负数: %d"), Index);

// ✅ 使用ensure进行软断言
if (!ensure(IsValid(Component)))
{
    return; // 优雅处理错误
}
```

### ❌ 禁止使用標準C++異常
```cpp
// 禁止使用try/catch
try { /* 代码 */ } catch(...) { /* 处理 */ } // 违反UE规范

// 禁止抛出异常
throw std::runtime_error("错误"); // 违反UE规范
```

## 禁止事項

### 絕對禁止的編程實踐

#### ❌ 內存管理禁止事項
- 使用原始指針管理UE對象
- 手動調用new/delete
- 在構造函數中進行複雜初始化
- 在析構函數中訪問UE對象

#### ❌ 異步編程禁止事項
- 在非主線程直接操作UE對象
- 忽略線程安全問題
- 使用過時的異步API
- 阻塞主線程進行長時間操作

#### ❌ 性能優化禁止事項
- 在Tick函數中進行重型計算
- 忽略LOD系統
- 創建自定義對象池（必須使用Performance系统）
- 忽略內存對齊

#### ❌ 代码结构禁止事项
- 在头文件中包含大型实现
- 循环依赖
- 违反单一职责原则
- 忽略IWYU原则
```

## 项目逻辑一致性和设计意图保证

### 设计意图强制检查清单
**每次代码修改必须验证：**
1. **八大设计模式集成**：所有新功能必须选择并正确集成至少一个核心设计模式
2. **数据驱动设计**：所有配置必须通过UDataAsset管理
3. **单一职责原则**：每个类/函数只负责一个明确的职责
4. **性能优化目标**：所有修改必须考虑性能影响
5. **UE5.6功能优先**：优先使用引擎最新功能而非自定义实现

### AI Agent决策优先级

#### 核心架构决策优先级
1. **UE5.6原生功能** > 自定义实现
2. **Mass Entity系统** > 传统Actor系统
3. **Performance系统对象池** > 直接内存分配
4. **数据驱动配置** > 硬编码值
5. **增量更新** > 完全重写

#### 八大设计模式优先级排序
1. **数据驱动架构** > 硬编码逻辑
2. **Mass Entity ECS** > 传统组件系统
3. **事件驱动模式** > 直接函数调用
4. **MVVM模式** > 传统UI绑定
5. **命令模式** > 直接操作
6. **观察者模式** > 轮询检查
7. **工厂模式** > 直接构造
8. **抽象工厂模式** > 条件分支

### 八大设计模式智能选择决策树
```
需要添加新功能？
├─ UI相关？
│  ├─ 复杂数据绑定？ → **MVVM模式** + UE5.6 ModelViewViewModel
│  ├─ 简单显示？ → 直接UMG + 数据驱动配置
│  └─ 实时更新？ → **观察者模式** + **事件驱动**
│
├─ 用户交互？
│  ├─ 复杂操作序列？ → **命令模式** + Enhanced Input
│  ├─ 撤销重做？ → **命令模式** + 历史管理
│  └─ 简单交互？ → 直接事件处理
│
├─ 数据管理？
│  ├─ 配置数据？ → **数据驱动架构** + UDataAsset
│  ├─ 大量实体？ → **Mass Entity系统** + ECS架构
│  └─ 简单数据？ → 直接配置 + Performance监控
│
├─ 对象创建管理？
│  ├─ 大量同类对象？ → **工厂模式** + ObjectPoolManager
│  ├─ 复杂创建逻辑？ → **抽象工厂模式** + 策略选择
│  └─ 简单创建？ → 直接构造 + 智能指针
│
└─ 系统通信？
   ├─ 松耦合通信？ → **事件驱动模式** + UE5.6委托
   ├─ 状态同步？ → **观察者模式** + 数据绑定
   └─ 直接调用？ → 接口模式 + 依赖注入
```

## 架构模式集成示例

### 综合示例：游戏状态管理系统
```cpp
// 【多模式协同】MVVM + 数据驱动 + 事件驱动 + 命令模式集成
UCLASS()
class GAME_API UGameStateManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【数据驱动】使用UDataAsset配置游戏状态
    UPROPERTY(EditAnywhere, Category = "Configuration")
    TObjectPtr<UGameStateConfigDataAsset> GameStateConfig;

    // 【MVVM集成】ViewModel用于UI数据绑定
    UPROPERTY(BlueprintReadOnly, Category = "MVVM")
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【事件驱动】状态变更事件
    DECLARE_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, EGameState, EGameState);
    FOnGameStateChanged OnGameStateChanged;

    // 【命令模式】状态变更命令
    UFUNCTION(BlueprintCallable, Category = "Game State")
    void ChangeGameState(EGameState NewState)
    {
        // 创建状态变更命令
        TObjectPtr<UGameStateChangeCommand> Command = NewObject<UGameStateChangeCommand>();
        Command->Initialize(CurrentState, NewState, this);

        // 执行命令
        CommandManager->ExecuteCommand(Command);
    }

private:
    EGameState CurrentState = EGameState::MainMenu;

    UPROPERTY()
    TObjectPtr<UCommandManager> CommandManager;
};
```

```

## 测试和验证规范

### 必须进行的测试类型
```cpp
// ✅ 性能测试要求
void TestPerformance()
{
    // 帧率测试：确保60FPS稳定运行
    // 内存测试：监控内存使用不超过预算
    // 批处理测试：验证Mass Entity批处理效率
}

// ✅ 稳定性测试要求
void TestStability()
{
    // 长时间运行测试
    // 内存泄漏检测
    // 异常情况处理验证
}
```








































### 🖥️ UE5.6 MVVM模块强制使用规范

#### ✅ ViewModel基类标准实现
```cpp
// 基于UE5.6 ModelViewViewModel模块的ViewModel基类
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameDataViewModel : public UMVVMViewModelBase
{
    GENERATED_BODY()

public:
    // 【数据绑定】使用FieldNotify进行属性通知
    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    int32 PlayerScore = 0;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    float PlayerHealth = 100.0f;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Player Data")
    FString PlayerName = TEXT("Player");

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Game State")
    bool bIsGamePaused = false;

    // 【UE5.6最佳实践】使用UE_MVVM_SET_PROPERTY_VALUE宏更新属性
    UFUNCTION(BlueprintCallable, Category = "Player Data")
    void UpdatePlayerScore(int32 NewScore)
    {
        if (NewScore != PlayerScore)
        {
            UE_MVVM_SET_PROPERTY_VALUE(PlayerScore, NewScore);
            UE_LOG(LogTemp, Log, TEXT("【MVVM数据】玩家分数更新: %d"), NewScore);

            // 【性能优化】触发相关计算属性更新
            OnPlayerScoreChanged();
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Player Data")
    void UpdatePlayerHealth(float NewHealth)
    {
        const float ClampedHealth = FMath::Clamp(NewHealth, 0.0f, 100.0f);
        if (!FMath::IsNearlyEqual(ClampedHealth, PlayerHealth))
        {
            UE_MVVM_SET_PROPERTY_VALUE(PlayerHealth, ClampedHealth);
            UE_LOG(LogTemp, VeryVerbose, TEXT("【MVVM数据】玩家血量更新: %.1f"), ClampedHealth);

            // 【事件驱动】触发血量变化事件
            OnPlayerHealthChanged();
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Game State")
    void SetGamePaused(bool bPaused)
    {
        if (bPaused != bIsGamePaused)
        {
            UE_MVVM_SET_PROPERTY_VALUE(bIsGamePaused, bPaused);
            UE_LOG(LogTemp, Log, TEXT("【MVVM状态】游戏暂停状态: %s"), bPaused ? TEXT("暂停") : TEXT("继续"));
        }
    }

    // 【计算属性】基于现有数据的派生属性
    UFUNCTION(BlueprintPure, Category = "Player Data")
    FText GetPlayerScoreText() const
    {
        return FText::Format(NSLOCTEXT("Game", "ScoreFormat", "Score: {0}"), PlayerScore);
    }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    float GetHealthPercentage() const
    {
        return PlayerHealth / 100.0f;
    }

    UFUNCTION(BlueprintPure, Category = "Player Data")
    bool IsPlayerAlive() const
    {
        return PlayerHealth > 0.0f;
    }

protected:
    // 【事件处理】属性变化回调
    virtual void OnPlayerScoreChanged()
    {
        // 可在子类中重写以处理分数变化逻辑
        if (PlayerScore > 0 && PlayerScore % 1000 == 0)
        {
            UE_LOG(LogTemp, Log, TEXT("【成就系统】玩家达到里程碑分数: %d"), PlayerScore);
        }
    }

    virtual void OnPlayerHealthChanged()
    {
        // 可在子类中重写以处理血量变化逻辑
        if (PlayerHealth <= 20.0f && PlayerHealth > 0.0f)
        {
            UE_LOG(LogTemp, Warning, TEXT("【警告系统】玩家血量危险: %.1f"), PlayerHealth);
        }
    }
};

// 专门用于Mass Entity数据的ViewModel
UCLASS(BlueprintType)
class GAME_API UMassEntityViewModel : public UMVVMViewModelBase
{
    GENERATED_BODY()

public:
    // 【Mass Entity集成】实体数据绑定
    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    int32 EntityCount = 0;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    float AveragePerformanceLevel = 1.0f;

    UPROPERTY(BlueprintReadOnly, FieldNotify, Category = "Mass Entity")
    int32 ActiveProcessorCount = 0;

    // 【性能监控】更新Mass Entity统计数据
    UFUNCTION(BlueprintCallable, Category = "Mass Entity")
    void UpdateMassEntityStats(int32 NewEntityCount, float NewPerformanceLevel, int32 NewProcessorCount)
    {
        bool bNeedsUpdate = false;

        if (NewEntityCount != EntityCount)
        {
            UE_MVVM_SET_PROPERTY_VALUE(EntityCount, NewEntityCount);
            bNeedsUpdate = true;
        }

        if (!FMath::IsNearlyEqual(NewPerformanceLevel, AveragePerformanceLevel))
        {
            UE_MVVM_SET_PROPERTY_VALUE(AveragePerformanceLevel, NewPerformanceLevel);
            bNeedsUpdate = true;
        }

        if (NewProcessorCount != ActiveProcessorCount)
        {
            UE_MVVM_SET_PROPERTY_VALUE(ActiveProcessorCount, NewProcessorCount);
            bNeedsUpdate = true;
        }

        if (bNeedsUpdate)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity MVVM】统计数据更新: 实体数=%d, 性能=%.2f, 处理器数=%d"),
                   EntityCount, AveragePerformanceLevel, ActiveProcessorCount);
        }
    }

    // 【计算属性】Mass Entity性能指标
    UFUNCTION(BlueprintPure, Category = "Mass Entity")
    FText GetEntityCountText() const
    {
        return FText::Format(NSLOCTEXT("Game", "EntityCountFormat", "Entities: {0}"), EntityCount);
    }

    UFUNCTION(BlueprintPure, Category = "Mass Entity")
    FLinearColor GetPerformanceColor() const
    {
        // 根据性能级别返回颜色
        if (AveragePerformanceLevel >= 0.8f)
        {
            return FLinearColor::Green;
        }
        else if (AveragePerformanceLevel >= 0.5f)
        {
            return FLinearColor::Yellow;
        }
        else
        {
            return FLinearColor::Red;
        }
    }
};
```

#### ✅ View层标准实现（UMG集成）
```cpp
// 基于UMG的MVVM View实现
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameHUDWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    virtual void NativeConstruct() override
    {
        Super::NativeConstruct();
        SetupMVVMBindings();
        UE_LOG(LogTemp, Log, TEXT("【MVVM View】游戏HUD界面初始化完成"));
    }

protected:
    // 【MVVM绑定】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM", meta = (BindWidget))
    TObjectPtr<UGameDataViewModel> GameDataViewModel;

    // 【UMG组件】UI控件引用
    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> ScoreText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UProgressBar> HealthBar;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> PlayerNameText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> PauseButton;

    // 【MVVM设置】建立数据绑定
    void SetupMVVMBindings()
    {
        if (!IsValid(GameDataViewModel))
        {
            // 【对象创建】创建ViewModel实例
            GameDataViewModel = NewObject<UGameDataViewModel>(this);
        }

        // 【UE5.6 MVVM】使用UMVVMSubsystem建立绑定
        if (UMVVMSubsystem* MVVMSubsystem = GEngine->GetEngineSubsystem<UMVVMSubsystem>())
        {
            if (UMVVMView* MVVMView = MVVMSubsystem->GetViewFromUserWidget(this))
            {
                // 建立ViewModel到View的绑定
                MVVMView->SetViewModel(FMVVMViewModelContext(GameDataViewModel));
                UE_LOG(LogTemp, Log, TEXT("【MVVM绑定】ViewModel绑定成功"));
            }
        }

        // 【事件绑定】UI事件处理
        if (IsValid(PauseButton))
        {
            PauseButton->OnClicked.AddDynamic(this, &UGameHUDWidget::OnPauseButtonClicked);
        }
    }

    // 【UI事件处理】暂停按钮点击
    UFUNCTION()
    void OnPauseButtonClicked()
    {
        if (IsValid(GameDataViewModel))
        {
            const bool bNewPausedState = !GameDataViewModel->bIsGamePaused;
            GameDataViewModel->SetGamePaused(bNewPausedState);
            UE_LOG(LogTemp, Log, TEXT("【UI交互】暂停按钮点击，新状态: %s"),
                   bNewPausedState ? TEXT("暂停") : TEXT("继续"));
        }
    }

    // 【蓝图接口】供蓝图调用的ViewModel访问
    UFUNCTION(BlueprintPure, Category = "MVVM")
    UGameDataViewModel* GetGameDataViewModel() const
    {
        return GameDataViewModel;
    }

    // 【数据更新】手动更新UI（用于非绑定场景）
    UFUNCTION(BlueprintCallable, Category = "UI")
    void RefreshUIData()
    {
        if (!IsValid(GameDataViewModel))
        {
            return;
        }

        // 【性能优化】只在数据变化时更新UI
        if (IsValid(ScoreText))
        {
            ScoreText->SetText(GameDataViewModel->GetPlayerScoreText());
        }

        if (IsValid(HealthBar))
        {
            HealthBar->SetPercent(GameDataViewModel->GetHealthPercentage());
        }

        if (IsValid(PlayerNameText))
        {
            PlayerNameText->SetText(FText::FromString(GameDataViewModel->PlayerName));
        }
    }
};
```

### 🏗️ UE5.6数据驱动架构规范

#### ✅ UDataAsset数据管理实现
```cpp
// 游戏配置数据资产基类
UCLASS(BlueprintType, Abstract)
class GAME_API UGameConfigDataAsset : public UDataAsset
{
    GENERATED_BODY()

public:
    // 【数据验证】验证配置数据完整性
    virtual bool ValidateData() const PURE_VIRTUAL(UGameConfigDataAsset::ValidateData, return false;);

    // 【热重载支持】配置变更通知
    virtual void OnConfigChanged() PURE_VIRTUAL(UGameConfigDataAsset::OnConfigChanged,);

    // 【版本控制】配置版本管理
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Version")
    int32 ConfigVersion = 1;

protected:
    // 【事件通知】配置变化通知
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnConfigChanged, UGameConfigDataAsset*);
    FOnConfigChanged OnConfigChangedEvent;

public:
    // 【事件访问】获取配置变化事件
    FOnConfigChanged& GetConfigChangedEvent() { return OnConfigChangedEvent; }
};

// 玩家配置数据资产实现
UCLASS(BlueprintType)
class GAME_API UPlayerConfigDataAsset : public UGameConfigDataAsset
{
    GENERATED_BODY()

public:
    // 【核心配置】玩家基础配置
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Player Config")
    int32 MaxScore = 999999;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Player Config")
    float MaxHealth = 100.0f;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Player Config")
    int32 MaxLevel = 100;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Player Config")
    FString DefaultPlayerName = TEXT("Player");

    // 【配置验证】验证玩家配置
    virtual bool ValidateData() const override
    {
        return MaxScore > 0 && MaxHealth > 0.0f &&
               MaxLevel > 0 && !DefaultPlayerName.IsEmpty();
    }

    // 【热重载】配置变更处理
    virtual void OnConfigChanged() override
    {
        UE_LOG(LogTemp, Log, TEXT("【配置热重载】玩家配置已更新"));
        OnConfigChangedEvent.Broadcast(this);
    }
};
```

#### ✅ Mass Entity数据组件集成
```cpp
// Mass Entity玩家数据组件
USTRUCT()
struct GAME_API FPlayerDataFragment : public FMassFragment
{
    GENERATED_BODY()

    // 【Mass Entity】玩家核心数据
    UPROPERTY()
    int32 Score = 0;

    UPROPERTY()
    float Health = 100.0f;

    UPROPERTY()
    int32 Level = 1;

    UPROPERTY()
    FString PlayerName = TEXT("Player");

    // 【配置引用】关联的配置数据
    UPROPERTY()
    TObjectPtr<UPlayerConfigDataAsset> ConfigAsset;

    // 【数据验证】验证组件数据
    bool IsValid() const
    {
        return Health >= 0.0f && Score >= 0 && Level >= 1 && !PlayerName.IsEmpty();
    }

    // 【配置应用】应用配置数据
    void ApplyConfig(const UPlayerConfigDataAsset* Config)
    {
        if (IsValid(Config))
        {
            Health = FMath::Clamp(Health, 0.0f, Config->MaxHealth);
            Score = FMath::Clamp(Score, 0, Config->MaxScore);
            Level = FMath::Clamp(Level, 1, Config->MaxLevel);
            ConfigAsset = const_cast<UPlayerConfigDataAsset*>(Config);
        }
    }
};

// Mass Entity玩家数据处理器
UCLASS()
class GAME_API UPlayerDataProcessor : public UMassProcessor
{
    GENERATED_BODY()

public:
    virtual void Initialize(UObject& Owner) override
    {
        Super::Initialize(Owner);
        UE_LOG(LogTemp, Log, TEXT("【Mass Entity】玩家数据处理器初始化完成"));
    }

protected:
    virtual void ConfigureQueries(const TSharedRef<FMassEntityManager>& EntityManager) override
    {
        EntityQuery.AddRequirement<FPlayerDataFragment>(EMassFragmentAccess::ReadWrite);
        EntityQuery.AddRequirement<FTransformFragment>(EMassFragmentAccess::ReadOnly);
    }

    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        // 【批处理】批量处理玩家数据
        EntityQuery.ForEachEntityChunk(EntityManager, Context,
            [this](FMassExecutionContext& Context)
            {
                const TArrayView<FPlayerDataFragment> PlayerDataFragments =
                    Context.GetMutableFragmentView<FPlayerDataFragment>();

                for (int32 EntityIndex = 0; EntityIndex < Context.GetNumEntities(); ++EntityIndex)
                {
                    ProcessPlayerData(PlayerDataFragments[EntityIndex]);
                }
            });
    }

private:
    void ProcessPlayerData(FPlayerDataFragment& PlayerData)
    {
        // 【数据验证】验证玩家数据
        if (!PlayerData.IsValid())
        {
            UE_LOG(LogTemp, Warning, TEXT("【数据验证】玩家数据无效，正在修复"));
            FixInvalidPlayerData(PlayerData);
        }

        // 【配置同步】同步配置变更
        if (IsValid(PlayerData.ConfigAsset))
        {
            PlayerData.ApplyConfig(PlayerData.ConfigAsset);
        }
    }

    void FixInvalidPlayerData(FPlayerDataFragment& PlayerData)
    {
        if (PlayerData.Health < 0.0f) PlayerData.Health = 0.0f;
        if (PlayerData.Score < 0) PlayerData.Score = 0;
        if (PlayerData.Level < 1) PlayerData.Level = 1;
        if (PlayerData.PlayerName.IsEmpty()) PlayerData.PlayerName = TEXT("Player");
    }

    FMassEntityQuery EntityQuery;
};
```

#### ✅ 数据驱动管理器实现
```cpp
// 数据驱动管理器基类
UCLASS(BlueprintType)
class GAME_API UDataDrivenManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);
        LoadAllConfigurations();
        UE_LOG(LogTemp, Log, TEXT("【数据驱动】管理器初始化完成"));
    }

    // 【配置加载】加载所有配置数据
    UFUNCTION(BlueprintCallable, Category = "Data Management")
    void LoadAllConfigurations()
    {
        LoadPlayerConfigurations();
        LoadGameplayConfigurations();
        LoadPerformanceConfigurations();
    }

    // 【热重载】重新加载配置
    UFUNCTION(BlueprintCallable, Category = "Data Management")
    void ReloadConfigurations()
    {
        UE_LOG(LogTemp, Log, TEXT("【热重载】开始重新加载配置"));
        LoadAllConfigurations();
        OnConfigurationsReloaded.Broadcast();
    }

protected:
    // 【事件通知】配置重载完成事件
    DECLARE_MULTICAST_DELEGATE(FOnConfigurationsReloaded);
    FOnConfigurationsReloaded OnConfigurationsReloaded;

    // 【配置存储】各类配置数据
    UPROPERTY(BlueprintReadOnly, Category = "Configurations")
    TObjectPtr<UPlayerConfigDataAsset> PlayerConfig;

    UPROPERTY(BlueprintReadOnly, Category = "Configurations")
    TObjectPtr<UGameplayConfigDataAsset> GameplayConfig;

    UPROPERTY(BlueprintReadOnly, Category = "Configurations")
    TObjectPtr<UPerformanceConfigDataAsset> PerformanceConfig;

private:
    void LoadPlayerConfigurations();
    void LoadGameplayConfigurations();
    void LoadPerformanceConfigurations();

public:
    // 【访问接口】获取配置数据
    UFUNCTION(BlueprintPure, Category = "Data Access")
    UPlayerConfigDataAsset* GetPlayerConfig() const { return PlayerConfig; }

    UFUNCTION(BlueprintPure, Category = "Data Access")
    UGameplayConfigDataAsset* GetGameplayConfig() const { return GameplayConfig; }

    UFUNCTION(BlueprintPure, Category = "Data Access")
    UPerformanceConfigDataAsset* GetPerformanceConfig() const { return PerformanceConfig; }

    // 【事件访问】获取重载事件
    FOnConfigurationsReloaded& GetConfigReloadedEvent() { return OnConfigurationsReloaded; }
};

// 玩家数据管理器实现
UCLASS(BlueprintType)
class GAME_API UPlayerDataManager : public UDataDrivenManager
{
    GENERATED_BODY()

public:
    // 【玩家数据】处理玩家输入
    UFUNCTION(BlueprintCallable, Category = "Player Management")
    void HandleScoreChange(int32 ScoreToAdd, const FString& Reason = TEXT(""))
    {
        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            // 【Mass Entity】通过ECS系统处理分数变化
            ProcessScoreChangeCommand(ScoreToAdd, Reason);
        }
    }

    UFUNCTION(BlueprintCallable, Category = "Player Management")
    void HandleHealthChange(float HealthDelta, const FString& Source = TEXT(""))
    {
        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            // 【Mass Entity】通过ECS系统处理血量变化
            ProcessHealthChangeCommand(HealthDelta, Source);
        }
    }

    // 【配置管理】应用玩家配置
    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void ApplyPlayerConfiguration(UPlayerConfigDataAsset* NewConfig)
    {
        if (IsValid(NewConfig) && NewConfig->ValidateData())
        {
            PlayerConfig = NewConfig;
            OnPlayerConfigChanged.Broadcast(NewConfig);
            UE_LOG(LogTemp, Log, TEXT("【配置应用】玩家配置已更新"));
        }
    }

protected:
    // 【事件通知】玩家配置变更事件
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerConfigChanged, UPlayerConfigDataAsset*);
    FOnPlayerConfigChanged OnPlayerConfigChanged;

    // 【命令处理】处理分数变化命令
    void ProcessScoreChangeCommand(int32 ScoreToAdd, const FString& Reason)
    {
        // 创建Mass Entity命令来处理分数变化
        if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
        {
            // 实现具体的分数变化逻辑
            UE_LOG(LogTemp, Log, TEXT("【数据驱动】处理分数变化: +%d, 原因: %s"), ScoreToAdd, *Reason);
        }
    }

    // 【命令处理】处理血量变化命令
    void ProcessHealthChangeCommand(float HealthDelta, const FString& Source)
    {
        // 创建Mass Entity命令来处理血量变化
        if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
        {
            // 实现具体的血量变化逻辑
            UE_LOG(LogTemp, Log, TEXT("【数据驱动】处理血量变化: %.1f, 来源: %s"), HealthDelta, *Source);
        }
    }

    // 【配置加载】加载玩家配置
    virtual void LoadPlayerConfigurations() override
    {
        // 从数据资产加载玩家配置
        const FString ConfigPath = TEXT("/Game/Data/PlayerConfig");
        PlayerConfig = LoadObject<UPlayerConfigDataAsset>(nullptr, *ConfigPath);

        if (IsValid(PlayerConfig))
        {
            UE_LOG(LogTemp, Log, TEXT("【配置加载】玩家配置加载成功"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("【配置加载】玩家配置加载失败，使用默认配置"));
            CreateDefaultPlayerConfig();
        }
    }

private:
    void CreateDefaultPlayerConfig()
    {
        PlayerConfig = NewObject<UPlayerConfigDataAsset>(this);
        // 设置默认值
        PlayerConfig->MaxScore = 999999;
        PlayerConfig->MaxHealth = 100.0f;
        PlayerConfig->MaxLevel = 100;
        PlayerConfig->DefaultPlayerName = TEXT("Player");
    }

public:
    // 【事件访问】获取配置变更事件
    FOnPlayerConfigChanged& GetPlayerConfigChangedEvent() { return OnPlayerConfigChanged; }
        {
            ViewModel->UpdatePlayerScore(PlayerData->Score);
            ViewModel->UpdatePlayerHealth(PlayerData->Health);
            // 其他数据同步...
        }
    }

private:
    UGameDataViewModel* FindActiveViewModel()
    {
        // 实现查找当前活动ViewModel的逻辑
        // 这里简化处理，实际项目中可能需要更复杂的查找逻辑
        return nullptr;
    }
};
```

### 📊 数据绑定最佳实践

#### ✅ 双向数据绑定规范
```cpp
// 数据绑定管理器
UCLASS()
class GAME_API UDataBindingManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【双向绑定】建立Model和ViewModel之间的双向绑定
    template<typename ModelType, typename ViewModelType>
    void CreateTwoWayBinding(ModelType* Model, ViewModelType* ViewModel)
    {
        static_assert(TIsDerivedFrom<ModelType, UGameModel>::IsDerived,
                     "ModelType must derive from UGameModel");
        static_assert(TIsDerivedFrom<ViewModelType, UMVVMViewModelBase>::IsDerived,
                     "ViewModelType must derive from UMVVMViewModelBase");

        if (!IsValid(Model) || !IsValid(ViewModel))
        {
            UE_LOG(LogTemp, Error, TEXT("【数据绑定】无效的Model或ViewModel"));
            return;
        }

        // 【Model到ViewModel】监听Model变化更新ViewModel
        Model->GetDataChangedEvent().AddUObject(this, &UDataBindingManager::OnModelChanged);

        // 【绑定记录】记录绑定关系
        FDataBindingPair BindingPair;
        BindingPair.Model = Model;
        BindingPair.ViewModel = ViewModel;
        ActiveBindings.Add(BindingPair);

        UE_LOG(LogTemp, Log, TEXT("【数据绑定】双向绑定建立成功: %s <-> %s"),
               *Model->GetClass()->GetName(), *ViewModel->GetClass()->GetName());
    }

    // 【性能优化】批量更新绑定
    UFUNCTION(BlueprintCallable, Category = "Data Binding")
    void BatchUpdateBindings()
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(DataBindingManager_BatchUpdate);

        for (const FDataBindingPair& Binding : ActiveBindings)
        {
            if (IsValid(Binding.Model) && IsValid(Binding.ViewModel))
            {
                UpdateViewModelFromModel(Binding.Model, Binding.ViewModel);
            }
        }
    }

protected:
    // 绑定关系结构
    USTRUCT()
    struct FDataBindingPair
    {
        GENERATED_BODY()

        UPROPERTY()
        TObjectPtr<UGameModel> Model = nullptr;

        UPROPERTY()
        TObjectPtr<UMVVMViewModelBase> ViewModel = nullptr;
    };

private:
    // 【绑定存储】活动绑定列表
    UPROPERTY()
    TArray<FDataBindingPair> ActiveBindings;

    // 【事件处理】Model变化处理
    UFUNCTION()
    void OnModelChanged(UGameModel* ChangedModel)
    {
        // 查找对应的ViewModel并更新
        for (const FDataBindingPair& Binding : ActiveBindings)
        {
            if (Binding.Model == ChangedModel && IsValid(Binding.ViewModel))
            {
                UpdateViewModelFromModel(ChangedModel, Binding.ViewModel);
                break;
            }
        }
    }

    void UpdateViewModelFromModel(UGameModel* Model, UMVVMViewModelBase* ViewModel)
    {
        // 根据具体类型进行数据同步
        if (UPlayerDataModel* PlayerModel = Cast<UPlayerDataModel>(Model))
        {
            if (UGameDataViewModel* GameViewModel = Cast<UGameDataViewModel>(ViewModel))
            {
                GameViewModel->UpdatePlayerScore(PlayerModel->Score);
                GameViewModel->UpdatePlayerHealth(PlayerModel->Health);
                // 其他属性同步...
            }
        }
    }
};
```

### ❌ 严格禁止的数据驱动架构实现

#### ❌ 禁止的数据管理违规模式
```cpp
// 禁止硬编码配置数据
class BadHardcodedConfig
{
public:
    void BadConfigSetup()
    {
        // 错误：硬编码配置值，违反数据驱动原则
        const int32 MaxPlayerHealth = 100; // 应该从UDataAsset加载
        const float MovementSpeed = 600.0f; // 应该从配置文件读取
        const FString DefaultWeapon = TEXT("Sword"); // 应该数据驱动
    }
};

// 禁止绕过UDataAsset系统
class BadDirectDataAccess
{
    // 错误：直接修改配置数据，绕过数据验证
    UPROPERTY()
    TObjectPtr<UPlayerConfigDataAsset> ConfigAsset;

public:
    void BadModifyConfig()
    {
        // 错误：直接修改配置数据，没有验证和事件通知
        ConfigAsset->MaxHealth = 999; // 违反数据安全原则
        ConfigAsset->MaxScore = -1; // 可能导致数据不一致
    }
};

// 禁止在Mass Entity外处理大规模数据
class BadLargeScaleDataProcessing
{
public:
    void BadProcessPlayerData()
    {
        // 错误：使用传统循环处理大量数据，性能低下
        TArray<AActor*> AllPlayers;
        UGameplayStatics::GetAllActorsOfClass(GetWorld(), APlayerCharacter::StaticClass(), AllPlayers);

        for (AActor* Player : AllPlayers) // 违反Mass Entity原则
        {
            // 传统Actor系统处理，性能差
            ProcessSinglePlayer(Cast<APlayerCharacter>(Player));
        }
    }

private:
    void ProcessSinglePlayer(APlayerCharacter* Player) { /* 低效处理 */ }
};

// 禁止混合使用传统Actor和Mass Entity
class BadMixedArchitecture
{
    // 错误：同时使用Actor和Mass Entity处理相同类型数据
    UPROPERTY()
    TArray<AActor*> TraditionalActors; // 传统Actor系统

    UPROPERTY()
    TArray<FMassEntityHandle> MassEntities; // Mass Entity系统

public:
    void BadMixedProcessing()
    {
        // 错误：混合处理方式，导致架构不一致
        ProcessTraditionalActors(); // 传统方式
        ProcessMassEntities(); // Mass Entity方式
        // 两种方式处理相同逻辑，违反一致性原则
    }
};

// 禁止忽略数据生命周期管理
class BadDataLifecycleManagement
{
public:
    void BadDataHandling()
    {
        // 错误：不遵循UE5.6数据资产生命周期
        UPlayerConfigDataAsset* Config = NewObject<UPlayerConfigDataAsset>(); // 临时创建
        Config->MaxHealth = 100; // 直接设置
        // 错误：没有正确的生命周期管理，可能导致内存泄漏
    }
};
```

#### ❌ 禁止的UE5.6数据处理模式
```cpp
// 禁止忽略UE5.6数据验证机制
class BadDataValidation
{
    void BadConfigValidation()
    {
        // 错误：跳过数据验证，直接使用可能无效的配置
        UPlayerConfigDataAsset* Config = LoadConfigAsset();
        PlayerMaxHealth = Config->MaxHealth; // 没有验证Config是否有效
        // 应该先调用Config->ValidateData()
    }
};

// 禁止在非主线程修改UDataAsset
class BadAsyncDataModification
{
    void BadAsyncConfigUpdate()
    {
        AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, []()
        {
            // 错误：在后台线程直接修改UDataAsset
            ConfigAsset->MaxHealth = 200; // 违反UE5.6线程安全原则
            // UDataAsset修改必须在主线程进行
        });
    }
};

// 禁止数据资产之间的循环引用
class BadDataAssetDependency : public UDataAsset
{
    // 错误：数据资产之间形成循环依赖
    UPROPERTY(EditAnywhere)
    TObjectPtr<UOtherConfigDataAsset> OtherConfig; // 如果OtherConfig也引用此类，形成循环

public:
    virtual bool ValidateData() const override
    {
        // 错误：在验证中访问可能导致循环依赖的资产
        if (IsValid(OtherConfig))
        {
            return OtherConfig->ValidateData(); // 可能导致无限递归
        }
        return false;
    }
};

// 禁止绕过Mass Entity系统的批处理优化
class BadSingleEntityProcessing
{
public:
    void BadProcessEntities()
    {
        // 错误：逐个处理Mass Entity，失去批处理优化
        for (const FMassEntityHandle& Entity : AllEntities)
        {
            ProcessSingleEntity(Entity); // 违反Mass Entity批处理原则
        }
        // 应该使用FMassEntityQuery进行批处理
    }
};
```

## 架构模式集成示例

### 🎯 综合示例：游戏状态管理系统

#### ✅ 多模式协同架构设计
```cpp
// 【综合示例】游戏状态管理系统 - 集成事件驱动、命令模式、MVVM、Mass Entity
UCLASS()
class GAME_API UGameStateManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【事件驱动】游戏状态变更事件
    DECLARE_MULTICAST_DELEGATE_TwoParams(FOnGameStateChanged, EGameState /*OldState*/, EGameState /*NewState*/);
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnPlayerScoreChanged, int32 /*NewScore*/);
    DECLARE_MULTICAST_DELEGATE_OneParam(FOnMassEntityStatsChanged, const FMassEntityStats& /*Stats*/);

    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);
        InitializeArchitectureComponents();
        UE_LOG(LogTemp, Log, TEXT("【架构集成】游戏状态管理系统初始化完成"));
    }

    // 【命令模式】执行状态变更命令
    UFUNCTION(BlueprintCallable, Category = "Game State")
    void ExecuteStateChangeCommand(TSubclassOf<UGameStateCommand> CommandClass, EGameState NewState)
    {
        // 【对象池优化】从池中获取命令对象
        if (UCommandPoolManager* CommandPool = GetWorld()->GetSubsystem<UCommandPoolManager>())
        {
            if (TObjectPtr<UGameStateCommand> Command = CommandPool->GetPooledCommand<UGameStateCommand>())
            {
                // 【命令配置】设置命令参数
                Command->SetTargetState(NewState);
                Command->SetCurrentState(CurrentGameState);
                Command->SetStateManager(this);

                // 【命令队列】加入执行队列
                if (UMassCommandProcessor* CommandProcessor = GetWorld()->GetSubsystem<UMassCommandProcessor>())
                {
                    CommandProcessor->AddCommand(Command);
                    UE_LOG(LogTemp, Log, TEXT("【命令模式】状态变更命令已加入队列: %s -> %s"),
                           *UEnum::GetValueAsString(CurrentGameState), *UEnum::GetValueAsString(NewState));
                }
            }
        }
    }

    // 【数据驱动集成】更新配置数据和ViewModel
    void UpdateGameStateData(EGameState NewState, int32 PlayerScore, const FMassEntityStats& EntityStats)
    {
        // 【配置更新】更新游戏状态配置
        if (IsValid(GameStateConfig))
        {
            GameStateConfig->CurrentState = NewState;
            GameStateConfig->OnConfigChanged();
        }

        // 【MVVM集成】更新ViewModel数据
        if (IsValid(GameStateViewModel))
        {
            GameStateViewModel->SetGameState(NewState);
            GameStateViewModel->UpdatePlayerScore(PlayerScore);
            GameStateViewModel->UpdateMassEntityStats(EntityStats.EntityCount, EntityStats.AveragePerformance, EntityStats.ProcessorCount);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【数据驱动】ViewModel和配置数据已更新"));
        }
    }

    // 【Mass Entity集成】处理实体统计数据
    void ProcessMassEntityStats()
    {
        if (UMassEntitySubsystem* MassSubsystem = GetWorld()->GetSubsystem<UMassEntitySubsystem>())
        {
            FMassEntityManager& EntityManager = MassSubsystem->GetMutableEntityManager();

            // 【性能统计】收集Mass Entity统计信息
            FMassEntityStats Stats;
            Stats.EntityCount = EntityManager.GetNumEntities();
            Stats.AveragePerformance = CalculateAveragePerformance(EntityManager);
            Stats.ProcessorCount = GetActiveProcessorCount();

            // 【事件驱动】广播统计数据变更事件
            OnMassEntityStatsChanged.Broadcast(Stats);

            // 【数据驱动更新】同步到配置和ViewModel
            UpdateGameStateData(CurrentGameState, CurrentPlayerScore, Stats);
        }
    }

protected:
    // 【当前状态】游戏状态数据
    UPROPERTY(BlueprintReadOnly, Category = "Game State")
    EGameState CurrentGameState = EGameState::MainMenu;

    UPROPERTY(BlueprintReadOnly, Category = "Game State")
    int32 CurrentPlayerScore = 0;

    // 【MVVM组件】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM")
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【数据驱动组件】配置数据和管理器引用
    UPROPERTY(BlueprintReadOnly, Category = "Data Driven")
    TObjectPtr<UGameStateConfigDataAsset> GameStateConfig;

    UPROPERTY(BlueprintReadOnly, Category = "Data Driven")
    TObjectPtr<UDataDrivenManager> DataManager;

    // 【事件处理】状态变更处理
    UFUNCTION()
    void HandleGameStateChanged(EGameState OldState, EGameState NewState)
    {
        CurrentGameState = NewState;

        // 【数据驱动集成】更新配置数据
        if (IsValid(GameStateConfig))
        {
            GameStateConfig->CurrentState = NewState;
            GameStateConfig->OnConfigChanged();
        }

        // 【Performance优化】根据状态调整性能设置
        if (UPerformanceOptimizer* Optimizer = GetWorld()->GetSubsystem<UPerformanceOptimizer>())
        {
            Optimizer->AdjustPerformanceForGameState(NewState);
        }

        UE_LOG(LogTemp, Log, TEXT("【状态管理】游戏状态变更: %s -> %s"),
               *UEnum::GetValueAsString(OldState), *UEnum::GetValueAsString(NewState));
    }

    UFUNCTION()
    void HandlePlayerScoreChanged(int32 NewScore)
    {
        CurrentPlayerScore = NewScore;

        // 【数据驱动集成】更新配置数据
        if (IsValid(DataManager))
        {
            DataManager->UpdatePlayerScore(NewScore);
        }

        // 【MVVM集成】更新ViewModel
        if (IsValid(GameStateViewModel))
        {
            GameStateViewModel->UpdatePlayerScore(NewScore);
        }
    }

private:
    // 【架构初始化】初始化所有架构组件
    void InitializeArchitectureComponents()
    {
        // 【MVVM初始化】创建ViewModel
        GameStateViewModel = NewObject<UGameStateViewModel>(this);

        // 【数据驱动初始化】创建配置和管理器
        GameStateConfig = NewObject<UGameStateConfigDataAsset>(this);
        DataManager = GetWorld()->GetSubsystem<UDataDrivenManager>();

        // 【事件绑定】绑定事件处理器
        OnGameStateChanged.AddUObject(this, &UGameStateManager::HandleGameStateChanged);
        OnPlayerScoreChanged.AddUObject(this, &UGameStateManager::HandlePlayerScoreChanged);

        // 【配置绑定】建立配置和ViewModel之间的绑定
        if (IsValid(GameStateConfig))
        {
            GameStateConfig->GetConfigChangedEvent().AddUObject(this, &UGameStateManager::OnConfigurationChanged);
        }
    }

    // 【配置变更处理】处理配置数据变更
    UFUNCTION()
    void OnConfigurationChanged(UGameConfigDataAsset* ChangedConfig)
    {
        if (UGameStateConfigDataAsset* StateConfig = Cast<UGameStateConfigDataAsset>(ChangedConfig))
        {
            // 【热重载】应用新的配置数据
            CurrentGameState = StateConfig->CurrentState;

            // 【ViewModel同步】同步到ViewModel
            if (IsValid(GameStateViewModel))
            {
                GameStateViewModel->SetGameState(StateConfig->CurrentState);
            }

            UE_LOG(LogTemp, Log, TEXT("【配置热重载】游戏状态配置已更新"));
        }
    }

    // 【性能计算】计算平均性能
    float CalculateAveragePerformance(FMassEntityManager& EntityManager)
    {
        // 简化的性能计算逻辑
        const int32 EntityCount = EntityManager.GetNumEntities();
        return EntityCount > 0 ? FMath::Clamp(1000.0f / EntityCount, 0.1f, 1.0f) : 1.0f;
    }

    int32 GetActiveProcessorCount()
    {
        // 简化的处理器计数逻辑
        return 5; // 假设有5个活动处理器
    }

public:
    // 【事件访问器】公开事件委托
    FOnGameStateChanged OnGameStateChanged;
    FOnPlayerScoreChanged OnPlayerScoreChanged;
    FOnMassEntityStatsChanged OnMassEntityStatsChanged;
};

// 【数据结构】Mass Entity统计信息
USTRUCT(BlueprintType)
struct GAME_API FMassEntityStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    int32 EntityCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    float AveragePerformance = 1.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Mass Entity")
    int32 ProcessorCount = 0;
};

// 【枚举定义】游戏状态
UENUM(BlueprintType)
enum class EGameState : uint8
{
    MainMenu        UMETA(DisplayName = "Main Menu"),
    Loading         UMETA(DisplayName = "Loading"),
    InGame          UMETA(DisplayName = "In Game"),
    Paused          UMETA(DisplayName = "Paused"),
    GameOver        UMETA(DisplayName = "Game Over")
};
```

#### ✅ 游戏状态变更命令实现
```cpp
// 【命令模式】游戏状态变更命令
UCLASS()
class GAME_API UGameStateCommand : public UMassCommand
{
    GENERATED_BODY()

public:
    virtual void Execute(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        if (!IsValid(StateManager))
        {
            UE_LOG(LogTemp, Error, TEXT("【命令执行】StateManager无效"));
            return;
        }

        const double StartTime = FPlatformTime::Seconds();

        // 【状态验证】检查状态转换是否有效
        if (!IsValidStateTransition(CurrentState, TargetState))
        {
            UE_LOG(LogTemp, Warning, TEXT("【命令执行】无效的状态转换: %s -> %s"),
                   *UEnum::GetValueAsString(CurrentState), *UEnum::GetValueAsString(TargetState));
            return;
        }

        // 【事件驱动】广播状态变更事件
        StateManager->OnGameStateChanged.Broadcast(CurrentState, TargetState);

        // 【Mass Entity集成】根据状态调整Mass Entity处理
        AdjustMassEntityProcessing(EntityManager, Context, TargetState);

        // 【Performance优化】记录执行时间
        const double EndTime = FPlatformTime::Seconds();
        TrackCommandPerformance(StartTime, EndTime);

        UE_LOG(LogTemp, Log, TEXT("【命令执行】状态变更命令执行完成: %s -> %s"),
               *UEnum::GetValueAsString(CurrentState), *UEnum::GetValueAsString(TargetState));
    }

    virtual void Undo(FMassEntityManager& EntityManager, FMassExecutionContext& Context) override
    {
        if (IsValid(StateManager))
        {
            // 【撤销操作】恢复到之前的状态
            StateManager->OnGameStateChanged.Broadcast(TargetState, CurrentState);
            AdjustMassEntityProcessing(EntityManager, Context, CurrentState);

            UE_LOG(LogTemp, Log, TEXT("【命令撤销】状态变更已撤销: %s -> %s"),
                   *UEnum::GetValueAsString(TargetState), *UEnum::GetValueAsString(CurrentState));
        }
    }

    virtual bool CanUndo() const override { return true; }
    virtual bool CanBatch() const override { return false; } // 状态变更不能批处理

    // 【命令配置】设置命令参数
    void SetTargetState(EGameState InTargetState) { TargetState = InTargetState; }
    void SetCurrentState(EGameState InCurrentState) { CurrentState = InCurrentState; }
    void SetStateManager(UGameStateManager* InStateManager) { StateManager = InStateManager; }

protected:
    virtual FString GetCommandName() const override
    {
        return FString::Printf(TEXT("GameStateCommand_%s_to_%s"),
                              *UEnum::GetValueAsString(CurrentState),
                              *UEnum::GetValueAsString(TargetState));
    }

private:
    UPROPERTY()
    EGameState CurrentState = EGameState::MainMenu;

    UPROPERTY()
    EGameState TargetState = EGameState::MainMenu;

    UPROPERTY()
    TObjectPtr<UGameStateManager> StateManager = nullptr;

    // 【业务逻辑】验证状态转换
    bool IsValidStateTransition(EGameState From, EGameState To) const
    {
        // 定义有效的状态转换规则
        switch (From)
        {
            case EGameState::MainMenu:
                return To == EGameState::Loading;
            case EGameState::Loading:
                return To == EGameState::InGame;
            case EGameState::InGame:
                return To == EGameState::Paused || To == EGameState::GameOver;
            case EGameState::Paused:
                return To == EGameState::InGame || To == EGameState::MainMenu;
            case EGameState::GameOver:
                return To == EGameState::MainMenu;
            default:
                return false;
        }
    }

    // 【Mass Entity集成】根据状态调整实体处理
    void AdjustMassEntityProcessing(FMassEntityManager& EntityManager, FMassExecutionContext& Context, EGameState NewState)
    {
        switch (NewState)
        {
            case EGameState::InGame:
                // 激活所有游戏相关的Mass Processor
                EnableGameplayProcessors(EntityManager);
                break;
            case EGameState::Paused:
                // 暂停非关键的Mass Processor
                PauseNonCriticalProcessors(EntityManager);
                break;
            case EGameState::MainMenu:
                // 禁用游戏相关的Mass Processor
                DisableGameplayProcessors(EntityManager);
                break;
            default:
                break;
        }
    }

    void EnableGameplayProcessors(FMassEntityManager& EntityManager)
    {
        // 实现激活游戏处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】激活游戏处理器"));
    }

    void PauseNonCriticalProcessors(FMassEntityManager& EntityManager)
    {
        // 实现暂停非关键处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】暂停非关键处理器"));
    }

    void DisableGameplayProcessors(FMassEntityManager& EntityManager)
    {
        // 实现禁用游戏处理器的逻辑
        UE_LOG(LogTemp, VeryVerbose, TEXT("【Mass Entity】禁用游戏处理器"));
    }
};
```

#### ✅ UI集成示例：游戏状态HUD
```cpp
// 【MVVM+UMG集成】游戏状态HUD界面
UCLASS(BlueprintType, Blueprintable)
class GAME_API UGameStateHUDWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    virtual void NativeConstruct() override
    {
        Super::NativeConstruct();
        SetupArchitectureIntegration();
        UE_LOG(LogTemp, Log, TEXT("【UI集成】游戏状态HUD初始化完成"));
    }

protected:
    // 【MVVM绑定】ViewModel引用
    UPROPERTY(BlueprintReadOnly, Category = "MVVM", meta = (BindWidget))
    TObjectPtr<UGameStateViewModel> GameStateViewModel;

    // 【UI组件】界面控件
    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> GameStateText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> PlayerScoreText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UTextBlock> EntityCountText;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UProgressBar> PerformanceBar;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> PauseButton;

    UPROPERTY(BlueprintReadOnly, Category = "UI", meta = (BindWidget))
    TObjectPtr<UButton> MenuButton;

    // 【架构集成】设置架构组件集成
    void SetupArchitectureIntegration()
    {
        // 【MVVM设置】建立ViewModel绑定
        if (!IsValid(GameStateViewModel))
        {
            GameStateViewModel = NewObject<UGameStateViewModel>(this);
        }

        // 【事件驱动】绑定游戏状态管理器事件
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            StateManager->OnGameStateChanged.AddUObject(this, &UGameStateHUDWidget::OnGameStateChanged);
            StateManager->OnPlayerScoreChanged.AddUObject(this, &UGameStateHUDWidget::OnPlayerScoreChanged);
            StateManager->OnMassEntityStatsChanged.AddUObject(this, &UGameStateHUDWidget::OnMassEntityStatsChanged);
        }

        // 【UI事件绑定】绑定按钮事件
        if (IsValid(PauseButton))
        {
            PauseButton->OnClicked.AddDynamic(this, &UGameStateHUDWidget::OnPauseButtonClicked);
        }

        if (IsValid(MenuButton))
        {
            MenuButton->OnClicked.AddDynamic(this, &UGameStateHUDWidget::OnMenuButtonClicked);
        }

        // 【MVVM绑定】使用UE5.6 MVVM系统
        if (UMVVMSubsystem* MVVMSubsystem = GEngine->GetEngineSubsystem<UMVVMSubsystem>())
        {
            if (UMVVMView* MVVMView = MVVMSubsystem->GetViewFromUserWidget(this))
            {
                MVVMView->SetViewModel(FMVVMViewModelContext(GameStateViewModel));
            }
        }
    }

    // 【事件处理】游戏状态变更响应
    UFUNCTION()
    void OnGameStateChanged(EGameState OldState, EGameState NewState)
    {
        // 【UI更新】更新状态显示
        if (IsValid(GameStateText))
        {
            FString StateDisplayName = UEnum::GetDisplayValueAsText(NewState).ToString();
            GameStateText->SetText(FText::FromString(StateDisplayName));
        }

        // 【UI逻辑】根据状态调整UI可见性
        UpdateUIVisibilityForState(NewState);

        UE_LOG(LogTemp, VeryVerbose, TEXT("【UI事件】游戏状态UI已更新: %s"),
               *UEnum::GetValueAsString(NewState));
    }

    UFUNCTION()
    void OnPlayerScoreChanged(int32 NewScore)
    {
        if (IsValid(PlayerScoreText))
        {
            PlayerScoreText->SetText(FText::Format(NSLOCTEXT("Game", "ScoreFormat", "Score: {0}"), NewScore));
        }
    }

    UFUNCTION()
    void OnMassEntityStatsChanged(const FMassEntityStats& Stats)
    {
        // 【性能显示】更新Mass Entity统计显示
        if (IsValid(EntityCountText))
        {
            EntityCountText->SetText(FText::Format(NSLOCTEXT("Game", "EntityCountFormat", "Entities: {0}"), Stats.EntityCount));
        }

        if (IsValid(PerformanceBar))
        {
            PerformanceBar->SetPercent(Stats.AveragePerformance);
        }
    }

    // 【命令模式集成】按钮点击处理
    UFUNCTION()
    void OnPauseButtonClicked()
    {
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            // 【命令执行】通过命令模式执行状态变更
            StateManager->ExecuteStateChangeCommand(UGameStateCommand::StaticClass(), EGameState::Paused);
        }
    }

    UFUNCTION()
    void OnMenuButtonClicked()
    {
        if (UGameStateManager* StateManager = GetWorld()->GetSubsystem<UGameStateManager>())
        {
            StateManager->ExecuteStateChangeCommand(UGameStateCommand::StaticClass(), EGameState::MainMenu);
        }
    }

private:
    // 【UI逻辑】根据状态更新UI可见性
    void UpdateUIVisibilityForState(EGameState NewState)
    {
        switch (NewState)
        {
            case EGameState::InGame:
                SetVisibility(ESlateVisibility::Visible);
                if (IsValid(PauseButton)) PauseButton->SetVisibility(ESlateVisibility::Visible);
                break;
            case EGameState::Paused:
                if (IsValid(PauseButton)) PauseButton->SetVisibility(ESlateVisibility::Collapsed);
                break;
            case EGameState::MainMenu:
                SetVisibility(ESlateVisibility::Collapsed);
                break;
            default:
                break;
        }
    }
};
```

### 🚀 架构模式集成最佳实践

#### ✅ 性能优化集成策略
```cpp
// 【性能优化】架构模式性能监控管理器
UCLASS()
class GAME_API UArchitecturePerformanceManager : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    // 【统计收集】收集各架构模式的性能统计
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void CollectArchitecturePerformanceStats()
    {
        TRACE_CPUPROFILER_EVENT_SCOPE(ArchitecturePerformanceManager_CollectStats);

        // 【事件驱动性能】统计事件处理性能
        CollectEventDrivenStats();

        // 【命令模式性能】统计命令执行性能
        CollectCommandPatternStats();

        // 【MVVM性能】统计数据绑定性能
        CollectMVVMStats();

        // 【集成性能】统计多模式协同性能
        CollectIntegrationStats();

        // 【性能报告】生成性能报告
        GeneratePerformanceReport();
    }

    // 【优化建议】基于统计数据提供优化建议
    UFUNCTION(BlueprintCallable, Category = "Performance")
    TArray<FString> GetOptimizationRecommendations() const
    {
        TArray<FString> Recommendations;

        // 【事件驱动优化】
        if (EventProcessingTime > 5.0f) // 5ms阈值
        {
            Recommendations.Add(TEXT("考虑使用异步事件处理减少主线程阻塞"));
            Recommendations.Add(TEXT("检查事件处理器中是否有耗时操作"));
        }

        // 【命令模式优化】
        if (CommandExecutionTime > 10.0f) // 10ms阈值
        {
            Recommendations.Add(TEXT("启用命令批处理以提高执行效率"));
            Recommendations.Add(TEXT("考虑使用对象池减少命令对象创建开销"));
        }

        // 【MVVM优化】
        if (DataBindingUpdateTime > 3.0f) // 3ms阈值
        {
            Recommendations.Add(TEXT("减少ViewModel属性更新频率"));
            Recommendations.Add(TEXT("使用批量更新机制优化数据绑定"));
        }

        // 【内存优化】
        if (MemoryUsage > 100.0f) // 100MB阈值
        {
            Recommendations.Add(TEXT("检查对象池配置，避免内存泄漏"));
            Recommendations.Add(TEXT("优化ViewModel和Command对象的生命周期管理"));
        }

        return Recommendations;
    }

private:
    // 【性能指标】各模式性能统计
    float EventProcessingTime = 0.0f;      // 事件处理时间(ms)
    float CommandExecutionTime = 0.0f;     // 命令执行时间(ms)
    float DataBindingUpdateTime = 0.0f;    // 数据绑定更新时间(ms)
    float MemoryUsage = 0.0f;              // 内存使用量(MB)
    int32 ActiveEventHandlers = 0;         // 活动事件处理器数量
    int32 PendingCommands = 0;             // 待处理命令数量
    int32 ActiveViewModels = 0;            // 活动ViewModel数量

    void CollectEventDrivenStats()
    {
        // 收集事件驱动模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectCommandPatternStats()
    {
        // 收集命令模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectMVVMStats()
    {
        // 收集MVVM模式的性能统计
        // 实现具体的统计逻辑
    }

    void CollectIntegrationStats()
    {
        // 收集多模式集成的性能统计
        // 实现具体的统计逻辑
    }

    void GeneratePerformanceReport()
    {
        UE_LOG(LogTemp, Log, TEXT("【性能报告】架构模式性能统计:"));
        UE_LOG(LogTemp, Log, TEXT("  事件处理时间: %.2fms"), EventProcessingTime);
        UE_LOG(LogTemp, Log, TEXT("  命令执行时间: %.2fms"), CommandExecutionTime);
        UE_LOG(LogTemp, Log, TEXT("  数据绑定时间: %.2fms"), DataBindingUpdateTime);
        UE_LOG(LogTemp, Log, TEXT("  内存使用量: %.2fMB"), MemoryUsage);
        UE_LOG(LogTemp, Log, TEXT("  活动组件: 事件处理器=%d, 命令=%d, ViewModel=%d"),
               ActiveEventHandlers, PendingCommands, ActiveViewModels);
    }
};
```

#### ✅ 架构模式集成指导原则

##### 🎯 模式选择决策矩阵
```
功能需求 vs 架构模式选择:

┌─────────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│   功能需求      │ 事件驱动    │ 命令模式    │   MVVM      │ 数据驱动    │
├─────────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ UI数据绑定      │     ○       │     ×       │     ●       │     ○       │
│ 组件间通信      │     ●       │     ○       │     ×       │     ○       │
│ 复杂操作序列    │     ○       │     ●       │     ×       │     ○       │
│ 撤销重做功能    │     ×       │     ●       │     ×       │     ○       │
│ 实时数据更新    │     ●       │     ○       │     ●       │     ●       │
│ 配置数据管理    │     ○       │     ○       │     ×       │     ●       │
│ 大规模数据处理  │     ○       │     ●       │     ○       │     ●       │
│ 性能敏感操作    │     ●       │     ●       │     ○       │     ●       │
└─────────────────┴─────────────┴─────────────┴─────────────┴─────────────┘

图例: ● 强烈推荐  ○ 适用  × 不推荐
```

##### 🔧 集成实施检查清单

**📋 事件驱动模式集成检查**
- [ ] 使用UE5.6原生委托系统 (DECLARE_MULTICAST_DELEGATE)
- [ ] 实现异步事件处理避免主线程阻塞
- [ ] 建立事件生命周期管理机制
- [ ] 集成Performance系统的对象池优化
- [ ] 添加事件处理性能监控

**📋 命令模式集成检查**
- [ ] 基于UMassCommand实现命令基类
- [ ] 集成Mass Entity系统的批处理机制
- [ ] 实现撤销/重做功能支持
- [ ] 使用对象池管理命令对象生命周期
- [ ] 建立命令队列优先级管理

**📋 MVVM模式集成检查**
- [ ] 基于UE5.6 ModelViewViewModel模块实现
- [ ] 使用UE_MVVM_SET_PROPERTY_VALUE宏更新属性
- [ ] 建立ViewModel与UMG的标准绑定
- [ ] 实现数据验证和格式化机制
- [ ] 集成Mass Entity数据的MVVM绑定

**📋 数据驱动架构集成检查**
- [ ] 实现UDataAsset配置数据的完整管理
- [ ] 建立配置数据变化的事件通知机制
- [ ] 集成Mass Entity系统处理大规模数据
- [ ] 实现配置数据的验证和热重载机制
- [ ] 建立数据驱动的单元测试架构

**📋 现代架构协同检查**
- [ ] 事件驱动 + 命令模式的无缝集成
- [ ] MVVM + Mass Entity的数据同步
- [ ] 数据驱动 + Performance系统的优化集成
- [ ] 所有模式与UE5.6 ECS架构的兼容性
- [ ] 统一的错误处理和日志记录

##### 🎯 常见集成陷阱及避免方法

**⚠️ 陷阱1: 过度设计**
- **问题**: 为简单功能使用复杂的架构模式组合
- **避免**: 根据功能复杂度选择合适的模式，简单功能使用简单实现

**⚠️ 陷阱2: 循环依赖**
- **问题**: 不同架构模式之间形成循环引用
- **避免**: 明确定义模式间的依赖方向，使用事件解耦

**⚠️ 陷阱3: 性能开销**
- **问题**: 多层架构导致不必要的性能开销
- **避免**: 使用性能监控，在关键路径上优化或简化架构

**⚠️ 陷阱4: 状态不一致**
- **问题**: 多个模式管理相同数据导致状态不同步
- **避免**: 建立单一数据源原则，使用事件驱动同步状态

**⚠️ 陷阱5: 内存泄漏**
- **问题**: 事件绑定和对象引用未正确清理
- **避免**: 实现完整的生命周期管理，使用弱引用避免循环引用

這個規範文件現在為AI Agent提供了完整的UE5.6項目開發指導，包含了具體的代碼示例、禁止事項、決策規則和文件交互規範。所有規則都使用命令式語言，專注於為AI提供明確的操作指導。
