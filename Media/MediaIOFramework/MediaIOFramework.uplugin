{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Media IO Framework", "Description": "Media Framework classes to support Professional Media IO used by the Virtual Production industry.", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "MediaIOCore", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "MediaIOEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}, {"Name": "GPUTextureTransfer", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "OpenColorIO", "Enabled": true}], "DocsURL": "", "IsExperimentalVersion": false}