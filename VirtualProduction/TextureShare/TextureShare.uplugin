{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Texture Share", "Description": "Share textures and data between processes", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "TextureShareCore", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "TextureShare", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "TextureShareDisplayCluster", "Type": "Runtime", "LoadingPhase": "PostDefault", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "nDisplay", "Enabled": true}]}