// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "StructUtils/InstancedStruct.h"
#include "UObject/Object.h"

#include "CustomizableObjectResourceData.generated.h"

/** Result of all the checks just before beginning an update. */
UENUM()
enum class ECOResourceDataType : uint16
{
	None, // Default.
	AssetUserData
};

/** ResourceData generated by a Customizable Object during compilation. */
USTRUCT()
struct FCustomizableObjectResourceData
{
	GENERATED_BODY()

	UPROPERTY()
	ECOResourceDataType Type = ECOResourceDataType::None;

	/** The data itself. This could be any USTRUCT. */
	UPROPERTY()
	FInstancedStruct Data;
};


/** A simple container that's used to store the FCustomizableObjectStreamedResourceData in a package */
UCLASS(MinimalAPI)
class UCustomizableObjectResourceDataContainer : public UObject
{
	GENERATED_BODY()

public:
	UPROPERTY()
	FCustomizableObjectResourceData Data;
};
