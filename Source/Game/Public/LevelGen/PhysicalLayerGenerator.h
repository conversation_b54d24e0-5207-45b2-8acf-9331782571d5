#pragma once

#include "CoreMinimal.h"
#include "LevelGen/MapUtil.h"
#include "Engine/Engine.h"
#include "PhysicalLayerGenerator.generated.h"

/**
 * 噪声类型枚举
 */
UENUM(BlueprintType)
enum class ENoiseType : uint8
{
    Perlin = 0,
    Simplex,
    Ridged,
    Billow,
    MAX UMETA(Hidden)
};

/**
 * 噪声参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FNoiseParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    ENoiseType NoiseType = ENoiseType::Perlin;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Frequency = 0.01f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    int32 Octaves = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Lacunarity = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    float Persistence = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Noise")
    int32 Seed = 0;

    FNoiseParams()
    {
        NoiseType = ENoiseType::Perlin;
        Frequency = 0.01f;
        Octaves = 4;
        Lacunarity = 2.0f;
        Persistence = 0.5f;
        Seed = 0;
    }
};

/**
 * 水文模拟参数
 */
USTRUCT(BlueprintType)
struct GAME_API FHydrologyParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    float RainfallAmount = 1.0f;        // 降雨量

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    float EvaporationRate = 0.1f;       // 蒸发率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    float FlowRate = 0.5f;              // 水流速率

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    int32 ErosionIterations = 10;       // 侵蚀迭代次数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    float ErosionStrength = 0.1f;       // 侵蚀强度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hydrology")
    float MinWaterLevel = 0.01f;        // 最小水位

    FHydrologyParams()
    {
        RainfallAmount = 1.0f;
        EvaporationRate = 0.1f;
        FlowRate = 0.5f;
        ErosionIterations = 10;
        ErosionStrength = 0.1f;
        MinWaterLevel = 0.01f;
    }
};

/**
 * 气候生成参数
 */
USTRUCT(BlueprintType)
struct GAME_API FClimateParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float BaseTemperature = 0.5f;       // 基础温度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float TemperatureRange = 0.4f;      // 温度变化范围

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float AltitudeEffect = 0.3f;        // 海拔对温度的影响

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    float LatitudeEffect = 0.2f;        // 纬度对温度的影响

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Climate")
    FNoiseParams TemperatureNoise;      // 温度噪声参数

    FClimateParams()
    {
        BaseTemperature = 0.5f;
        TemperatureRange = 0.4f;
        AltitudeEffect = 0.3f;
        LatitudeEffect = 0.2f;
        TemperatureNoise.Frequency = 0.005f;
        TemperatureNoise.Octaves = 3;
    }
};

/**
 * 风场生成参数
 */
USTRUCT(BlueprintType)
struct GAME_API FWindParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float BaseWindSpeed = 0.3f;         // 基础风速

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    FVector2D PrevailingWind = FVector2D(1.0f, 0.0f); // 主导风向

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float TerrainInfluence = 0.5f;      // 地形对风的影响

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float TemperatureInfluence = 0.3f;  // 温度对风的影响

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    float TurbulenceStrength = 0.2f;    // 湍流强度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Wind")
    FNoiseParams WindNoise;             // 风场噪声参数

    FWindParams()
    {
        BaseWindSpeed = 0.3f;
        PrevailingWind = FVector2D(1.0f, 0.0f);
        TerrainInfluence = 0.5f;
        TemperatureInfluence = 0.3f;
        TurbulenceStrength = 0.2f;
        WindNoise.Frequency = 0.02f;
        WindNoise.Octaves = 2;
    }
};

/**
 * 物理层生成参数结构体
 */
USTRUCT(BlueprintType)
struct GAME_API FPhysicalGenerationParams
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    FNoiseParams HeightNoise;           // 高度噪声参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    FHydrologyParams HydrologyParams;   // 水文参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    FClimateParams ClimateParams;       // 气候参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    FWindParams WindParams;             // 风场参数

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    float SeaLevel = 0.3f;              // 海平面高度

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Generation")
    float MountainThreshold = 0.7f;     // 山地阈值

    FPhysicalGenerationParams()
    {
        SeaLevel = 0.3f;
        MountainThreshold = 0.7f;
    }
};

/**
 * 物理层生成器
 * 负责生成地图的所有物理参数层
 */
UCLASS(BlueprintType, Blueprintable)
class GAME_API UPhysicalLayerGenerator : public UObject
{
    GENERATED_BODY()

public:
    UPhysicalLayerGenerator();

    // ========== 主要生成函数 ==========
    
    /**
     * 生成完整的物理层数据
     * @param Width 地图宽度
     * @param Height 地图高度
     * @param Params 生成参数
     * @return 生成的地图格子数组
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    TArray<FMapCell> GeneratePhysicalLayer(int32 Width, int32 Height, const FPhysicalGenerationParams& Params);

    // ========== 单独层生成函数 ==========
    
    /**
     * 生成高度图
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void GenerateHeightMap(TArray<FMapCell>& MapCells, int32 Width, int32 Height, const FNoiseParams& NoiseParams);

    /**
     * 执行水文模拟
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void SimulateHydrology(TArray<FMapCell>& MapCells, int32 Width, int32 Height, const FHydrologyParams& HydrologyParams);

    /**
     * 生成温度场
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void GenerateTemperatureField(TArray<FMapCell>& MapCells, int32 Width, int32 Height, const FClimateParams& ClimateParams);

    /**
     * 生成风场
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void GenerateWindField(TArray<FMapCell>& MapCells, int32 Width, int32 Height, const FWindParams& WindParams);

    /**
     * 生成土壤类型
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void GenerateSoilTypes(TArray<FMapCell>& MapCells, int32 Width, int32 Height, const FNoiseParams& SoilNoise);

    /**
     * 生成地表覆盖
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    void GenerateSurfaceCover(TArray<FMapCell>& MapCells, int32 Width, int32 Height);

    // ========== 噪声生成工具函数 ==========
    
    /**
     * 生成噪声值
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static float GenerateNoise(float X, float Y, const FNoiseParams& NoiseParams);

    /**
     * 生成分形噪声
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static float GenerateFractalNoise(float X, float Y, const FNoiseParams& NoiseParams);

    // ========== 默认参数获取函数 ==========
    
    /**
     * 获取默认高度噪声参数
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static FNoiseParams GetDefaultHeightNoiseParams();

    /**
     * 获取默认水文参数
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static FHydrologyParams GetDefaultHydrologyParams();

    /**
     * 获取默认气候参数
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static FClimateParams GetDefaultClimateParams();

    /**
     * 获取默认风场参数
     */
    UFUNCTION(BlueprintCallable, Category = "Physical Layer Generator")
    static FWindParams GetDefaultWindParams();

private:
    // ========== 私有辅助函数 ==========
    
    // 简单Perlin噪声实现
    static float PerlinNoise(float X, float Y, int32 Seed);
    
    // 计算梯度
    static float Gradient(int32 Hash, float X, float Y);
    
    // 平滑插值函数
    static float SmoothStep(float T);
    
    // 水流模拟辅助函数
    void SimulateWaterFlow(TArray<FMapCell>& MapCells, int32 Width, int32 Height, float FlowRate);
    
    // 侵蚀模拟辅助函数
    void SimulateErosion(TArray<FMapCell>& MapCells, int32 Width, int32 Height, float ErosionStrength);
    
    // 计算气压场
    void CalculatePressureField(const TArray<FMapCell>& MapCells, int32 Width, int32 Height, TArray<float>& PressureField);
    
    // 根据地形判断是否为风道
    bool IsWindChannel(const TArray<FMapCell>& MapCells, int32 Width, int32 Height, int32 X, int32 Y);
    
    // 获取格子索引
    static int32 GetCellIndex(int32 X, int32 Y, int32 Width);
    
    // 检查坐标有效性
    static bool IsValidCoordinate(int32 X, int32 Y, int32 Width, int32 Height);
};
