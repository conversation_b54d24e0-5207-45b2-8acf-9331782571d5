// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Containers/ContainersFwd.h"
#include "CoreTypes.h"

/**
 * SplashTextType defines the types of text on the splash screen
 */
namespace SplashTextType
{
	enum Type
	{
		/** Startup progress text */
		StartupProgress	= 0,

		/** Version information text line 1 */
		VersionInfo1,

		/** Copyright information text */
		CopyrightInfo,

		/** Game Name */
		GameName,

		// ...

		/** Number of text types (must be final enum value) */
		NumTextTypes
	};
}

class FString;
class FText;

/**
 * Generic implementation for most platforms
 */
struct FGenericPlatformSplash
{
	/** Show the splash screen. */
	FORCEINLINE static void Show() { }

	/** Hide the splash screen. */
	FORCEINLINE static void Hide() { }

	/**
	 * Sets a custom splash image to display
	 * 
	 * @param SplashFilename Full path to the splash image to display
	 */
	static APPLICATIONCORE_API void SetCustomSplashImage(const TCHAR* SplashFilename);


	/**
	 * Sets the progress displayed on the application icon (for startup/loading progress).
	 *
	 * @param InType Progress value in percent.
	 */
	FORCEINLINE static void SetProgress(int ProgressPercent)
	{
	
	}

	/**
	 * Sets the text displayed on the splash screen (for startup/loading progress)
	 *
	 * @param	InType		Type of text to change
	 * @param	InText		Text to display
	 */
	UE_DEPRECATED(5.6, "Use the overload of FPlatformSlash::SetSplashText that takes a FText")
	static APPLICATIONCORE_API void SetSplashText( const SplashTextType::Type InType, const TCHAR* InText );
	FORCEINLINE static void SetSplashText(const SplashTextType::Type InType, const FText& InText)
	{
	}

	/**
	 * Return whether the splash screen is being shown or not
	 */
	FORCEINLINE static bool IsShown()
	{
		return true;
	}

protected:
	/**
	* Finds a usable splash pathname for the given filename
	*
	* @param SplashFilename Name of the desired splash name("Splash")
	* @param IconFilename Name of the desired icon name("Splash")
	* @param OutPath String containing the path to the file, if this function returns true
	* @param OutIconPath String containing the path to the icon, if this function returns true
	*
	* @return true if a splash screen was found
	*/
	static APPLICATIONCORE_API bool GetSplashPath(const TCHAR* SplashFilename, FString& OutPath, bool& OutIsCustom);
	static APPLICATIONCORE_API bool GetSplashPath(const TCHAR* SplashFilename, const TCHAR* IconFilename, FString& OutPath, FString& OutIconPath, bool& OutIsCustom);
};
