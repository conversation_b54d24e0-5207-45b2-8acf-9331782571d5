<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>SQLite</Name>
  <Location>/Engine/Plugins/Runtime/GeoReferencing/Source/ThirdParty/vcpkg_installed</Location>
  <Function>SQLite is a C-language library that implements a small, fast, self-contained, high-reliability, full-featured, SQL database engine. It is a dependency for the PROJ library.</Function>
  <Eula>https://www.sqlite.org/copyright.html</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>