# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "UsdHydraDiscoveryPlugin": {
                        "bases": [
                            "NdrDiscoveryPlugin"
                        ], 
                        "displayName": "Discovery plugin for deprecated hydra shaders."
                    }, 
                    "UsdHydraGenerativeProceduralAPI": {
                        "alias": {
                            "UsdSchemaBase": "HydraGenerativeProceduralAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GenerativeProcedural"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "HydraGenerativeProceduralAPI", 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "LibraryPath": "../../../../../Source/ThirdParty/Linux/bin/x86_64-unknown-linux-gnu/libusd_usdHydra.so", 
            "Name": "usdHydra", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
