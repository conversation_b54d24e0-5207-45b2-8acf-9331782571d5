// Copyright Epic Games, Inc. All Rights Reserved.

#if defined(DEFINE_FORTNITE_MAIN_VERSIONS)
#define FORTNITE_MAIN_VERSION(Version, ID) Version,
#elif defined(CHECK_FORTNITE_MAIN_VERSIONS)
#define FORTNITE_MAIN_VERSION(Version, ID) static_assert(FFortniteMainBranchObjectVersion::Version == ID);
#endif

// Before any version changes were made
FORTNITE_MAIN_VERSION(BeforeCustomVersionWasAdded, 0)

// World composition tile offset changed from 2d to 3d
FORTNITE_MAIN_VERSION(WorldCompositionTile3DOffset, 1)

// Minor material serialization optimization
FORTNITE_MAIN_VERSION(MaterialInstanceSerializeOptimization_ShaderFName, 2)

// Refactored cull distances to account for HLOD, explicit override and globals in priority
FORTNITE_MAIN_VERSION(CullDistanceRefactor_RemovedDefaultDistance, 3)
FORTNITE_MAIN_VERSION(CullDistanceRefactor_NeverCullHLODsByDefault, 4)
FORTNITE_MAIN_VERSION(CullDistanceRefactor_NeverCullALODActorsByDefault, 5)

// Support to remove morphtarget generated by bRemapMorphtarget
FORTNITE_MAIN_VERSION(SaveGeneratedMorphTargetByEngine, 6)

// Convert reduction setting options
FORTNITE_MAIN_VERSION(ConvertReductionSettingOptions, 7)

// Serialize the type of blending used for landscape layer weight static params
FORTNITE_MAIN_VERSION(StaticParameterTerrainLayerWeightBlendType, 8)

// Fix up None Named animation curve names,
FORTNITE_MAIN_VERSION(FixUpNoneNameAnimationCurves, 9)

// Ensure ActiveBoneIndices to have parents even not skinned for old assets
FORTNITE_MAIN_VERSION(EnsureActiveBoneIndicesToContainParents, 10)

// Serialize the instanced static mesh render data, to avoid building it at runtime
FORTNITE_MAIN_VERSION(SerializeInstancedStaticMeshRenderData, 11)

// Cache material quality node usage
FORTNITE_MAIN_VERSION(CachedMaterialQualityNodeUsage, 12)

// Font outlines no longer apply to drop shadows for new objects but we maintain the opposite way for backwards compat
FORTNITE_MAIN_VERSION(FontOutlineDropShadowFixup, 13)

// New skeletal mesh import workflow (Geometry only or animation only re-import )
FORTNITE_MAIN_VERSION(NewSkeletalMeshImporterWorkflow, 14)

// Migrate data from previous data structure to new one to support materials per LOD on the Landscape
FORTNITE_MAIN_VERSION(NewLandscapeMaterialPerLOD, 15)

// New Pose Asset data type
FORTNITE_MAIN_VERSION(RemoveUnnecessaryTracksFromPose, 16)

// Migrate Foliage TLazyObjectPtr to TSoftObjectPtr
FORTNITE_MAIN_VERSION(FoliageLazyObjPtrToSoftObjPtr, 17)

// TimelineTemplates store their derived names instead of dynamically generating
// This code tied to this version was reverted and redone at a later date
FORTNITE_MAIN_VERSION(REVERTED_StoreTimelineNamesInTemplate, 18)

// Added BakePoseOverride for LOD setting
FORTNITE_MAIN_VERSION(AddBakePoseOverrideForSkeletalMeshReductionSetting, 19)

// TimelineTemplates store their derived names instead of dynamically generating
FORTNITE_MAIN_VERSION(StoreTimelineNamesInTemplate, 20)

// Avoid duplicating widget animations to save space.
FORTNITE_MAIN_VERSION(WidgetStopDuplicatingAnimations, 21)

// Allow reducing of the base LOD, we need to store some imported model data so we can reduce again from the same data.
FORTNITE_MAIN_VERSION(AllowSkeletalMeshToReduceTheBaseLOD, 22)

// Curve Table size reduction
FORTNITE_MAIN_VERSION(ShrinkCurveTableSize, 23)

// Widgets upgraded with WidgetStopDuplicatingAnimations, may not correctly default-to-self for the widget parameter.
FORTNITE_MAIN_VERSION(WidgetAnimationDefaultToSelfFail, 24)

// HUDWidgets now require an element tag
FORTNITE_MAIN_VERSION(FortHUDElementNowRequiresTag, 25)

// Animation saved as bulk data when cooked
FORTNITE_MAIN_VERSION(FortMappedCookedAnimation, 26)

// Support Virtual Bone in Retarget Manager
FORTNITE_MAIN_VERSION(SupportVirtualBoneInRetargeting, 27)

// Fixup bad defaults in water metadata
FORTNITE_MAIN_VERSION(FixUpWaterMetadata, 28)

// Move the location of water metadata
FORTNITE_MAIN_VERSION(MoveWaterMetadataToActor, 29)

// Replaced lake collision component
FORTNITE_MAIN_VERSION(ReplaceLakeCollision, 30)

// Anim layer node names are now conformed by Guid
FORTNITE_MAIN_VERSION(AnimLayerGuidConformation, 31)

// Ocean collision component has become dynamic
FORTNITE_MAIN_VERSION(MakeOceanCollisionTransient, 32)

// FFieldPath will serialize the owner struct reference and only a short path to its property
FORTNITE_MAIN_VERSION(FFieldPathOwnerSerialization, 33)

// Simplified WaterBody post process material handling
FORTNITE_MAIN_VERSION(FixUpUnderwaterPostProcessMaterial, 34)

// A single water exclusion volume can now exclude N water bodies
FORTNITE_MAIN_VERSION(SupportMultipleWaterBodiesPerExclusionVolume, 35)

// Serialize rigvm operators one by one instead of the full byte code array to ensure determinism
FORTNITE_MAIN_VERSION(RigVMByteCodeDeterminism, 36)

// Serialize the physical materials generated by the render material
FORTNITE_MAIN_VERSION(LandscapePhysicalMaterialRenderData, 37)

// RuntimeVirtualTextureVolume fix transforms
FORTNITE_MAIN_VERSION(FixupRuntimeVirtualTextureVolume, 38)

// Retrieve water body collision components that were lost in cooked builds
FORTNITE_MAIN_VERSION(FixUpRiverCollisionComponents, 39)

// Fix duplicate spline mesh components on rivers
FORTNITE_MAIN_VERSION(FixDuplicateRiverSplineMeshCollisionComponents, 40)

// Indicates level has stable actor guids
FORTNITE_MAIN_VERSION(ContainsStableActorGUIDs, 41)

// Levelset Serialization support for BodySetup.
FORTNITE_MAIN_VERSION(LevelsetSerializationSupportForBodySetup, 42)

// Moving Chaos solver properties to allow them to exist in the project physics settings
FORTNITE_MAIN_VERSION(ChaosSolverPropertiesMoved, 43)

// Moving some UFortGameFeatureData properties and behaviors into the UGameFeatureAction pattern
FORTNITE_MAIN_VERSION(GameFeatureData_MovedComponentListAndCheats, 44)

// Add centrifugal forces for cloth
FORTNITE_MAIN_VERSION(ChaosClothAddfictitiousforces, 45)

// Chaos Convex StructureData supports different index sizes based on num verts/planes
// Chaos FConvex uses array of FVec3s for vertices instead of particles
// (Merged from //UE4/Main)
FORTNITE_MAIN_VERSION(ChaosConvexVariableStructureDataAndVerticesArray, 46)

// Remove the WaterVelocityHeightTexture dependency on MPC_Landscape and LandscapeWaterIndo
FORTNITE_MAIN_VERSION(RemoveLandscapeWaterInfo, 47)

// Added the weighted value property type to store the cloths weight maps' low/high ranges
FORTNITE_MAIN_VERSION(ChaosClothAddWeightedValue, 48)

// Added the Long Range Attachment stiffness weight map
FORTNITE_MAIN_VERSION(ChaosClothAddTetherStiffnessWeightMap, 49)

// Fix corrupted LOD transition maps
FORTNITE_MAIN_VERSION(ChaosClothFixLODTransitionMaps, 50)

// Enable a few more weight maps to better art direct the cloth simulation
FORTNITE_MAIN_VERSION(ChaosClothAddTetherScaleAndDragLiftWeightMaps, 51)

// Enable material (edge, bending, and area stiffness) weight maps
FORTNITE_MAIN_VERSION(ChaosClothAddMaterialWeightMaps, 52)

// Added bShowCurve for movie scene float channel serialization
FORTNITE_MAIN_VERSION(SerializeFloatChannelShowCurve, 53)

// Minimize slack waste by using a single array for grass data
FORTNITE_MAIN_VERSION(LandscapeGrassSingleArray, 54)

// Add loop counters to sequencer's compiled sub-sequence data
FORTNITE_MAIN_VERSION(AddedSubSequenceEntryWarpCounter, 55)

// Water plugin is now component-based rather than actor based
FORTNITE_MAIN_VERSION(WaterBodyComponentRefactor, 56)

// Cooked BPGC storing editor-only asset tags
FORTNITE_MAIN_VERSION(BPGCCookedEditorTags, 57)

// Terrain layer weights are no longer considered material parameters
FORTNITE_MAIN_VERSION(TerrainLayerWeightsAreNotParameters, 58)

// Anim Dynamics Node Gravity Override vector is now defined in world space, not simulation space.
// Legacy behavior can be maintained with a flag, which is set false by default for new nodes,
// true for nodes predating this change.
FORTNITE_MAIN_VERSION(GravityOverrideDefinedInWorldSpace, 59)

// Anim Dynamics Node Physics parameters for each body in a chain are now stored in an array and can be edited.
FORTNITE_MAIN_VERSION(AnimDynamicsEditableChainParameters, 60)

// Decoupled the generation of the water texture from the Water Brush and the landscape
FORTNITE_MAIN_VERSION(WaterZonesRefactor, 61)

// Add faster damping calculations to the cloth simulation and rename previous Damping parameter to LocalDamping.
FORTNITE_MAIN_VERSION(ChaosClothFasterDamping, 62)

// Migrated function handlers to the CDO/archetype data
FORTNITE_MAIN_VERSION(MigratedFunctionHandlersToDefaults, 63)

// Storing inertia tensor as vec3 instead of matrix.
FORTNITE_MAIN_VERSION(ChaosInertiaConvertedToVec3, 64)

// Migrated event definitions to the CDO/archetype data
FORTNITE_MAIN_VERSION(MigratedEventDefinitionToDefaults, 65)

// Serialize LevelInstanceActorGuid on new ILevelInstanceInterface implementation
FORTNITE_MAIN_VERSION(LevelInstanceActorGuidSerialize, 66)

// Single-frame/key AnimDataModel patch-up
FORTNITE_MAIN_VERSION(SingleFrameAndKeyAnimModel, 67)

// Remapped bEvaluateWorldPositionOffset to bEvaluateWorldPositionOffsetInRayTracing
FORTNITE_MAIN_VERSION(RemappedEvaluateWorldPositionOffsetInRayTracing, 68)

// Water body collision settings are now those of the base UPrimitiveComponent, rather than duplicated in UWaterBodyComponent
FORTNITE_MAIN_VERSION(WaterBodyComponentCollisionSettingsRefactor, 69)

// Introducing widget inherited named slots.  This wouldn't have required a version bump, except in the previous
// version, users could make NamedSlots and then Seed them with any random widgets, as a sorta 'default' setup.
// In order to preserve that, we're bumping the version so that we can set a new field on UNamedSlot to control
// if a widget exposes its named slot to everyone (even if it has content) which by default they wont any longer.
FORTNITE_MAIN_VERSION(WidgetInheritedNamedSlots, 70)

// Added water HLOD material
FORTNITE_MAIN_VERSION(WaterHLODSupportAdded, 71)

// Moved parameters affecting Skeleton pose rendering from the PoseWatch class to the PoseWatchPoseElement class.
FORTNITE_MAIN_VERSION(PoseWatchMigrateSkeletonDrawParametersToPoseElement, 72)

// Reset default value for Water exclusion volumes to make them more intuitive and support the "it just works" philosophy.
FORTNITE_MAIN_VERSION(WaterExclusionVolumeExcludeAllDefault, 73)

// Added water non-tessellated LOD
FORTNITE_MAIN_VERSION(WaterNontessellatedLODSupportAdded, 74)

// Added FHierarchicalSimplification::SimplificationMethod
FORTNITE_MAIN_VERSION(HierarchicalSimplificationMethodEnumAdded, 75)

// Changed how world partition streaming cells are named
FORTNITE_MAIN_VERSION(WorldPartitionStreamingCellsNamingShortened, 76)

// Serialize ContentBundleGuid in WorldPartitionActorDesc
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeContentBundleGuid, 77)

// Serialize IsActorRuntimeOnly in WorldPartitionActorDesc
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeActorIsRuntimeOnly, 78)

// Add Nanite Material Override option to materials and material instances.
FORTNITE_MAIN_VERSION(NaniteMaterialOverride, 79)

// Serialize HLOD stats in HLODActorDesc
FORTNITE_MAIN_VERSION(WorldPartitionHLODActorDescSerializeStats, 80)

// WorldPartitionStreamingSourceComponent property deprecation
FORTNITE_MAIN_VERSION(WorldPartitionStreamingSourceComponentTargetDeprecation, 81)

// Fixed localization gathering for external actor packages
FORTNITE_MAIN_VERSION(FixedLocalizationGatherForExternalActorPackage, 82)

// Change HLODActors to RuntimeCells mapping to use a GUID instead of the cell name
FORTNITE_MAIN_VERSION(WorldPartitionHLODActorUseSourceCellGuid, 83)

// Add an attribute to geometry collection to track internal faces, rather than relying on material ID numbering
FORTNITE_MAIN_VERSION(ChaosGeometryCollectionInternalFacesAttribute, 84)

// Dynamic cast nodes use an enumerated pure node state to include a value for the default setting
FORTNITE_MAIN_VERSION(DynamicCastNodesUsePureStateEnum, 85)

// Add FWorldPartitionActorFilter to FLevelInstanceActorDesc/FDataLayerInstanceDesc
FORTNITE_MAIN_VERSION(WorldPartitionActorFilter, 86)

// Change the non-spatialized radius to blend to a pure 2D spatialized sound vs omnidirectional
FORTNITE_MAIN_VERSION(AudioAttenuationNonSpatializedRadiusBlend, 87)

// Serialize actor class descriptors
FORTNITE_MAIN_VERSION(WorldPartitionActorClassDescSerialize, 88)

// FActorContainerID is now an FGuid instead of a uint64
FORTNITE_MAIN_VERSION(WorldPartitionFActorContainerIDu64ToGuid, 89)

// FDataLayerInstanceDesc support for private data layers
FORTNITE_MAIN_VERSION(WorldPartitionPrivateDataLayers, 90)

// Reduce size and improve behaviour of Chaos::FImplicitObjectUnion
FORTNITE_MAIN_VERSION(ChaosImplicitObjectUnionBVHRefactor, 91)

// FLevelInstanceActorDesc DeltaSerialize Filter
FORTNITE_MAIN_VERSION(LevelInstanceActorDescDeltaSerializeFilter, 92)

// Fix the Nanite landscape mesh non-deterministic DDC keys
FORTNITE_MAIN_VERSION(FixNaniteLandscapeMeshDDCKey, 93)

// Change how connection graphs are stored on Geometry Collections to an edge-array representation
FORTNITE_MAIN_VERSION(ChaosGeometryCollectionConnectionEdgeGroup, 94)

// Moved the water info mesh data and static water body meshes into new static mesh components for water bodies.
FORTNITE_MAIN_VERSION(WaterBodyStaticMeshComponents, 95)

// Serialize invalid bounds in world partition actor descriptors
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeInvalidBounds, 96)

// Upgrade Navigation Links to use 64 bits for the ID
FORTNITE_MAIN_VERSION(NavigationLinkID32To64, 97)

// Serialize editor only references in world partition actor descriptors
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeEditorOnlyReferences, 98)

// Add support for soft object paths in actor descriptors
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeSoftObjectPathSupport, 99)

// Don't serialize class descriptor GUIDs
FORTNITE_MAIN_VERSION(WorldPartitionClasDescGuidTransient, 100)

// Serialize ActorDesc bIsMainWorldOnly
FORTNITE_MAIN_VERSION(WorldPartitionActorDescIsMainWorldOnly, 101)

// FWorldPartitionActorFilter go back to FString serialize of AssetPaths to avoid FArchiveReplaceOrClearExternalReferences clearing CDO references on BP Compile
FORTNITE_MAIN_VERSION(WorldPartitionActorFilterStringAssetPath, 102)

// Add FPackedLevelActorDesc for APackedLevelActor and support for APackedLevelActor Filters
FORTNITE_MAIN_VERSION(PackedLevelActorDesc, 103)

// Add customizable values for several UWorldPartitionRuntimeSpatialHash cvars
FORTNITE_MAIN_VERSION(WorldPartitionRuntimeSpatialHashCVarOverrides, 104)

// WorldPartition HLOD now contains a source actors object
FORTNITE_MAIN_VERSION(WorldPartitionHLODSourceActorsRefactor, 105)

FORTNITE_MAIN_VERSION(WaterBodyStaticMeshRename, 106)

// Geometry Collection now by-default converts vertex colors to sRGB when creating render data
FORTNITE_MAIN_VERSION(GeometryCollectionConvertVertexColorToSRGB, 107)

// Water bodies before this version need to update their water zone on load since they won't have been serialized yet.
FORTNITE_MAIN_VERSION(WaterOwningZonePointerFixup, 108)

// Set flags on water static meshes to duplicate transient to avoid underlying static mesh duplication issue
FORTNITE_MAIN_VERSION(WaterBodyStaticMeshDuplicateTransient, 109)

// Update paths to use the SkeletalClass
FORTNITE_MAIN_VERSION(MVVMConvertPropertyPathToSkeletalClass, 110)

// Fixup all flags/outering on static meshes on water bodies by rebuilding them completely
FORTNITE_MAIN_VERSION(WaterBodyStaticMeshFixup, 111)

// Binding extensions for anim graph nodes
FORTNITE_MAIN_VERSION(AnimGraphNodeBindingExtensions, 112)

// Function data stores a map from work to debug operands
FORTNITE_MAIN_VERSION(RigVMSaveDebugMapInGraphFunctionData, 113)

// Fix missing binding extensions for some anim graph nodes
FORTNITE_MAIN_VERSION(FixMissingAnimGraphNodeBindingExtensions, 114)

// EditableWhenInherited: Skip custom serialization on non Archetypes
FORTNITE_MAIN_VERSION(ISMComponentEditableWhenInheritedSkipSerialization, 115)

// GrassTypes are now per-component, rather than per-landscape proxy :
FORTNITE_MAIN_VERSION(LandscapeSupportPerComponentGrassTypes, 116)

// World partition actor data layers activation logic operator support defaults for old maps
FORTNITE_MAIN_VERSION(WorldPartitionDataLayersLogicOperatorAdded, 117)

// Started sorting Possessables, Spawnables, and MovieSceneBindings for better search performance.
FORTNITE_MAIN_VERSION(MovieSceneSortedBindings, 118)

// Remove the UAnimCurveCompressionCodec::InstanceGuid which causes cook determinism issues
FORTNITE_MAIN_VERSION(RemoveAnimCurveCompressionCodecInstanceGuid, 119)

// Serialize the source HLOD Layer for HLOD actor descriptors.
FORTNITE_MAIN_VERSION(WorldPartitionHLODActorDescSerializeSourceHLODLayer, 120)

// Serialize custom editor bounds for HLOD actor descriptors.
FORTNITE_MAIN_VERSION(WorldPartitionHLODActorDescSerializeEditorBounds, 121)

// Changed default Local Exposure Contrast from 1.0 to 0.8 (reverted)
FORTNITE_MAIN_VERSION(LocalExposureDefaultChangeFrom1_Reverted, 122)

// Added support of external packaging of Data Layer Instances
FORTNITE_MAIN_VERSION(AddDataLayerInstanceExternalPackage, 123)

// Update paths to keep a flag if they are the widget BP
FORTNITE_MAIN_VERSION(MVVMPropertyPathSelf, 124)

// Enabled ObjectPtr property serialization for Dataflow nodes
FORTNITE_MAIN_VERSION(AddDataflowObjectSerialization, 125)

// Add anim notify rate scaling, defaults to on for new content, off for old content
FORTNITE_MAIN_VERSION(AnimNotifyAddRateScale, 126)

// Fix tangents for non-uniform build scales, and add a flag to optionally match the previous (incorrect) tangents
FORTNITE_MAIN_VERSION(FixedTangentTransformForNonuniformBuildScale, 127)

// AnimNode Layers will now start in a Shared Group, instead of being each one on a different group at runtime
FORTNITE_MAIN_VERSION(AnimNodeRootDefaultGroupChange, 128)

// Move AnimNext graphs to sub-entries of assets
FORTNITE_MAIN_VERSION(AnimNextMoveGraphsToEntries, 129)

// Removed debug information containing compressed data author, time etc. from animation DDC data as it introduces indeterminism
FORTNITE_MAIN_VERSION(AnimationSequenceCompressedDataRemoveDebugData, 130)

// Changes to Orthographic Camera default settings
FORTNITE_MAIN_VERSION(OrthographicCameraDefaultSettings, 131)

// Added settings to Landscape HLODs
FORTNITE_MAIN_VERSION(LandscapeAddedHLODSettings, 132)

// Skeletal Mesh uses Mesh Description to store mesh bulk data.
FORTNITE_MAIN_VERSION(MeshDescriptionForSkeletalMesh, 133)

// Skeletal Mesh optionally cooks half edge data per lod
FORTNITE_MAIN_VERSION(SkeletalHalfEdgeData, 134)

// Combine graph contexts for AnimNext graphs
FORTNITE_MAIN_VERSION(AnimNextCombineGraphContexts, 135)

// Combine parameter blocks and graphs
FORTNITE_MAIN_VERSION(AnimNextCombineParameterBlocksAndGraphs, 136)

// Move workspaces to a seperate plugin
FORTNITE_MAIN_VERSION(AnimNextMoveWorkspaces, 137)

// Level Instance Property overrides
FORTNITE_MAIN_VERSION(LevelInstancePropertyOverrides, 138)

// Added FVolumetricLightMapGridDesc in MapBuildData
FORTNITE_MAIN_VERSION(VolumetricLightMapGridDescSupport, 139)

// Introduce new structure for customizing the landscape edit layer behavior
FORTNITE_MAIN_VERSION(IntroduceLandscapeEditLayerClass, 140)

// Change workspaces to store asset references as external objects
FORTNITE_MAIN_VERSION(AnimNextWorkspaceEntryConversion, 141)

// Add support for anytype in dataflow
FORTNITE_MAIN_VERSION(DataflowAnyTypeSupport, 142)

// Adding a new flag in RBAN Solver Setting to be able to use manifolds
FORTNITE_MAIN_VERSION(PhysicsAssetUseManifoldFlags, 143)

// Added support for to record sim and query data of Shape Instance data in CVD
FORTNITE_MAIN_VERSION(SimAndQueryDataSupportInChaosVisualDebugger, 144)

// Add the imported asset dependencies to the Cloth Asset USD Import node
FORTNITE_MAIN_VERSION(ChaosClothAssetUSDImportNodeAddAssetDependencies, 145)

// Changed HitLighting to HitLightingForReflections, and HitLighting now means hit lighting for entire Lumen
FORTNITE_MAIN_VERSION(LumenRayLightingModeOverrideEnum, 146)

// PCGPartitionActorDesc
FORTNITE_MAIN_VERSION(PCGPartitionActorDesc, 147)

// Target layers are now defined in the Landscape actor and not continuously synced from the assigned material.
FORTNITE_MAIN_VERSION(LandscapeTargetLayersInLandscapeActor, 148)

// Fix to get full name of templated type ( Tarray > TArray<Float> for example )
FORTNITE_MAIN_VERSION(DataflowTemplatedTypeFix, 149)

// Changes for LevelInstance support in StaticLighting
FORTNITE_MAIN_VERSION(LevelInstanceStaticLightingSupport, 150)

// PCGGridDescriptor
FORTNITE_MAIN_VERSION(PCGGridDescriptor, 151)

// AnimNext graphs now have public/private state
FORTNITE_MAIN_VERSION(AnimNextGraphAccessSpecifiers, 152)

// Added a more stable pixel depth offset mode.
FORTNITE_MAIN_VERSION(MaterialPixelDepthOffsetMode, 153)

// Added hideable pins to dataflow
FORTNITE_MAIN_VERSION(DataflowHideablePins, 154)

// Added multiple section import to the cloth asset skeletal mesh import node
FORTNITE_MAIN_VERSION(ClothAssetSkeletalMeshMultiSectionImport, 155)

// Serialize EditorBounds in WorldPartitionActorDesc
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeEditorBounds, 156)

// Fixup for the data that has been damaged by LandscapeTargetLayersInLandscapeActor (loss of landscape layer info object assignments)
FORTNITE_MAIN_VERSION(FixupLandscapeTargetLayersInLandscapeActor, 157)

//Allow custom import of morph target
FORTNITE_MAIN_VERSION(MorphTargetCustomImport, 158)

// Fix chaos cloth buckling stiffness parameter bug
FORTNITE_MAIN_VERSION(ChaosClothAllowZeroBucklingStiffness, 159)

// LevelSequenceUpgradeDynamicBindings was removed but was intended for this position. Putting this here to make sure versioning of subsequent assets remains the same
FORTNITE_MAIN_VERSION(LevelSequenceUpgradeDynamicBindings_NoOp, 160)

// AddToFrontend GFA now defaults to unload plugin on exit frontend
FORTNITE_MAIN_VERSION(GameFeatureDataActionAddToFrontendDefaultToUnload, 161)

// Upgraded movie scene 'dynamic bindings' to use the new Custom Bindings system
FORTNITE_MAIN_VERSION(LevelSequenceUpgradeDynamicBindings, 162)

// Changed the precision for the stored rotation on kinematic targets to match the precision used in particles
FORTNITE_MAIN_VERSION(ChaosStoreKinematicTargetRotationAsSinglePrecision, 163)

// PCG changes around the ApplyOnActor node, where we collapsed the TargetActor to the input pin.
FORTNITE_MAIN_VERSION(PCGApplyOnActorNodeMoveTargetActorEdgeToInput, 164)

// Deprecation of the bPlaying flag on FTimeline struct types in favor of a better
// PlayingStateTracker type to improve replication reliability
FORTNITE_MAIN_VERSION(TimelinePlayingStateTrackerDeprecation, 165)

// Enable SkipOnlyEditorOnly style cooking of UStaticMeshComponent::MeshPaintTexture
FORTNITE_MAIN_VERSION(MeshPaintTextureUsesEditorOnly, 166)

// Fixup and synchronize some landscape properties that have moved to the property sharing/overriding system :
FORTNITE_MAIN_VERSION(LandscapeBodyInstanceAsSharedProperty, 167)

// Multiple changes to AnimNext modules, variables etc.
FORTNITE_MAIN_VERSION(AnimNextModuleRefactor, 168)

// Subsurface profile now has a guid to be able to select one of many in a Substrate material.
FORTNITE_MAIN_VERSION(SubsurfaceProfileGuid, 169)

// Added support for to record the new solver iteration settings in CVD
FORTNITE_MAIN_VERSION(SolverIterationsDataSupportInChaosVisualDebugger, 170)

// Updated FColorMaterialInput to use FLinearColor instead of FColor
FORTNITE_MAIN_VERSION(MaterialInputUsesLinearColor, 171)

// Updated editor only AFunctionalTest running logic to run tests editor world if the actors don't support PIE
FORTNITE_MAIN_VERSION(FunctionalTestCanRunInEditorWorld, 172)

// Added support for display name in the Visual Logger
FORTNITE_MAIN_VERSION(VisualLoggerSupportDisplayName, 173)

// Added support for the GyroscopicTorque flag in CVD
FORTNITE_MAIN_VERSION(GyroscopicTorquesSupportInChaosVisualDebugger, 174)

// Added managed array property serialization
FORTNITE_MAIN_VERSION(AddManagedArrayCollectionPropertySerialization, 175)

// Landscape texture patches in Texture Asset source mode now use proper resolution when calculating transform
FORTNITE_MAIN_VERSION(LandscapeTexturePatchUsesTextureAssetResolution, 176)

// Added support for relative transform in WorldPartitionActorDesc
FORTNITE_MAIN_VERSION(WorldPartitionActorDescSerializeRelativeTransform, 177)

// Make sure scene graph entities are not public by default
FORTNITE_MAIN_VERSION(SceneGraphEntitiesPrivateByDefault, 178)

// Added debug color for physical materials
FORTNITE_MAIN_VERSION(DebugColorForPhysicalMaterials, 179)

// Added PreprocessedFontGeometry to FFontFaceData
FORTNITE_MAIN_VERSION(AddedPreprocessedFontGeometry, 180)

// Added Dynamic Mesh Sculpt Layer serialization
FORTNITE_MAIN_VERSION(DynamicMeshSerializeSculptLayers, 181)

// Fix reachable garbage object warnings from some legacy ASpatialHashRuntimeGridInfo actors
FORTNITE_MAIN_VERSION(SpatialHashRuntimeGridInfoSpriteFixup, 182)

// Removed UAnimSequence::bUseRawDataOnly flag alongside compression refactor
FORTNITE_MAIN_VERSION(AnimSequenceRawDataOnlyFlagRemoval, 183)

// HLOD relevancy of Level Instances was previously ignored, now taken into account. Reset to the default behavior.
FORTNITE_MAIN_VERSION(ResetLevelInstanceHLODRelevancy, 184)

// Updated default scene capture post-processing settings to reflect the underlying implementation overrides
FORTNITE_MAIN_VERSION(SceneCaptureDefaultSettings, 185)

// Add Cloth Asset Base class serialization
FORTNITE_MAIN_VERSION(AddClothAssetBase, 186)

// Add inline constant default values to the PCG graph nodes.
FORTNITE_MAIN_VERSION(PCGInlineConstantDefaultValues, 187)

// Add MaterialSubstrateSubsurfaceType type to UMaterialExpressionSubstrateSlabBSDF for replacing bUseSSSDifffusion
FORTNITE_MAIN_VERSION(AddMaterialSubstrateSubsurfaceType, 188)

// Added option to visualize runtime virtual textures' streamed mips only in PIE 
FORTNITE_MAIN_VERSION(AddedRuntimeVirtualTextureUseStreamingMipsInEditorMode, 189)

// Media plate holdout composite components have been replaced by a checkbox
FORTNITE_MAIN_VERSION(MediaPlateHoldoutComponentRemoval, 190)

// Changed PCG landscape cache default from "serialize at cook" to "never serialize"
FORTNITE_MAIN_VERSION(PCGLandscapeCacheDefaultSerializationChanged, 191)

// FSoftObjectPath::SubPathString changed to FUtf8String
FORTNITE_MAIN_VERSION(SoftObjectPathUtf8SubPaths, 192)

// FSoftObjectPath::SubPathString could be saved with trailing NULs and need truncating
FORTNITE_MAIN_VERSION(SoftObjectPathTrailingNULsMaintained, 193)

// Water body components no longer need to maintain their own PhysicalMaterial property since they are primitive components. After this version, leverage that one instead.
FORTNITE_MAIN_VERSION(WaterBodyPhysicalMaterialPropertyRemoval, 194)

// PCG fixed attribute set -> point conversion passing through empty point data as-is and violating output pin type.
FORTNITE_MAIN_VERSION(PCGAttributeSetToPointAlwaysConverts, 195)

//Add per material slot overlay material data
FORTNITE_MAIN_VERSION(MeshMaterialSlotOverlayMaterialAdded, 196)

// Convert Sustrate glint density properly
FORTNITE_MAIN_VERSION(ConvertGlintDensity, 197)

// Introduced skinweight validation to avoid render crashes and disappearing simulation meshes
FORTNITE_MAIN_VERSION(ClothAssetSkinweightsValidation, 198)

// Switching verse from right handed to left handed
FORTNITE_MAIN_VERSION(VerseRightToLeftHandedness, 199)

// Added additional data required to record and represent particle data from the game thread (Kinematic targets, and SQ rejection reasons)
FORTNITE_MAIN_VERSION(AdditionalGameThreadDataSupportInChaosVisualDebugger, 200)

// Upgrade UMG widget blueprints using legacy animation API
FORTNITE_MAIN_VERSION(UpgradeWidgetBlueprintLegacySequencePlayer, 201)

// Changed clockwise detection algorithm for PCGSplineDirection node with the correct one, but add a version to not break previous nodes.
FORTNITE_MAIN_VERSION(PCGSplineDirectionClockwiseFix, 202)

// Rect Lights set in EV units had the wrong intensity (older files need a flag set to keep the old look)
FORTNITE_MAIN_VERSION(RectLightFixedEVUnitConversion, 203)

// Add particle bounds to data exported to CVD
FORTNITE_MAIN_VERSION(ParticleInflatedBoundsInChaosVisualDebugger, 204)

// Migrate properties from FLandscapeLayer to ULandscapeEditLayer
FORTNITE_MAIN_VERSION(MigrateLandscapeEditLayerProperties, 205)

// Added more context data to CVD's traced shapes so we can play it back at the solver stage level (not just game thread frames) 
FORTNITE_MAIN_VERSION(ThreadContextDataInChaosVisualDebuggerDebugDrawData, 206)

// Changed default grid mode in surface sampler to a version that's more intuitive and less error-prone
FORTNITE_MAIN_VERSION(PCGChangedSurfaceSamplerDefaultGridCreationMode, 207)

// -----<new versions can be added above this line>-------------------------------------------------
#undef FORTNITE_MAIN_VERSION